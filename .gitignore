# Env vars
*.env
envvars.sh
/server/test/services/ai-chats/ai-chats.test.ts
/.idea/AugmentWebviewStateStore.xml
/server/test/services/bill-erasers/bill-erasers.test.ts
/server/test/services/calendars/calendars.test.ts
/server/test/services/caps/caps.test.ts
/server/test/services/claim-requests/claim-requests.test.ts
/server/test/services/cobras/cobras.test.ts
/server/test/services/contracts/contracts.test.ts
/server/test/services/exchanges/exchanges.test.ts
/server/test/services/fb-res/fb-res.test.ts
/server/test/services/fbs/fbs.test.ts
/server/test/services/funds/funds.test.ts
/server/test/services/gpps/gpps.test.ts
/server/test/services/gps/gps.test.ts
/server/test/services/guides/guides.test.ts
/server/test/services/health-shares/health-shares.test.ts
/server/test/services/ims/ims.test.ts
/server/test/services/mbrs/mbrs.test.ts
/server/test/services/networks/networks.test.ts
/server/test/services/offers/offers.test.ts
/server/test/services/price-estimates/price-estimates.test.ts
/server/test/services/pricebooks/pricebooks.test.ts
/client/quasar.config.js.temporary.compiled.1736984452864.mjs
/server/test/services/se-plans/se-plans.test.ts
/server/test/services/shops/shops.test.ts
/server/test/services/state-plans/state-plans.test.ts
/server/test/services/teams/teams.test.ts
/client/src/components/auth/utils/UcanDebugger.vue
/.idea/watcherTasks.xml
