version: '3.7'
services:
  # Run the Server application
  server-app:
    image: commoncare/server-app
    container_name: commoncare-server-app
    environment:
      - MONGO_DB_URI=${MONGO_DB_URI}
      - OAUTH_GOOGLE_KEY=${OAUTH_GOOGLE_KEY}
      - OAUTH_GOOGLE_SECRET=${OAUTH_GOOGLE_SECRET}
      - OAUTH_LINKEDIN_KEY=${OAUTH_LINKEDIN_KEY}
      - OAUTH_LINKEDIN_SECRET=${OAUTH_LINKEDIN_SECRET}
      - AUTH_SECRET=${AUTH_SECRET}
      - UCAN_SK=${UCAN_SK}
      - MAILGUN_API_KEY=${MAILGUN_API_KEY}
      - STORJ_ACCESS_KEY=${STORJ_ACCESS_KEY}
      - STORJ_REGION=${STORJ_REGION}
      - STORJ_BUCKET=${STORJ_BUCKET}
      - STORJ_ENDPOINT=${STORJ_ENDPOINT}
      - STORJ_SECRET_KEY=${STORJ_SECRET_KEY}
      - NODE_ENV=${NODE_ENV}
    restart: unless-stopped


  # Run the Client application
  client-app:
    image: commoncare/client-app
    container_name: commoncare-client-app
    restart: unless-stopped

  # Run the caddy server
  caddy:
    image: caddy/caddy:2.6.2-alpine
    container_name: caddy-commoncare-server-service
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "1234:1234"
      - "3030:3030"
    volumes:
      - $PWD/Caddyfile:/etc/caddy/Caddyfile
      - $PWD/site:/srv
      - caddy_data:/data
      - caddy_config:/config
volumes:
  caddy_data:
  caddy_config:
