<template>
  <div class="_fw">
    <provider-top v-if="providerAccounts?.length"></provider-top>
    <div class="row justify-center">
      <div class="_cent pd4 pw2">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
  import ProviderTop from 'components/providers/cards/ProviderTop.vue';
  import {useOrgs} from 'stores/orgs';
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';
  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const orgStore = useOrgs()

  const { item: org } = idGet({
    store: orgStore,
    value: getOrgId
  })

  const providerAccounts = computed(() => org.value?.providerAccounts || [])
</script>

<style lang="scss" scoped>

</style>
