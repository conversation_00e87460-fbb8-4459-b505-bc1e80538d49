<template>
  <div class="_fw">
    <div class="row justify-center">
      <div class="_xsent pd2 pw1 mnh80">
        <div class="__c __t">
          <q-tabs no-caps dense v-model="tab" align="left" indicator-color="secondary">
            <q-tab :name="t" v-for="(t, i) in Object.keys(tabs)" :key="`t-${i}`">
              <div class="flex items-center">
                <q-avatar size="20px" :color="tab === t ? 'secondary' : 'grey-5'">
                  <div class="_fa flex flex-center">
                    <div class="tw-six font-3-4r text-white">{{i+1}}</div>
                  </div>
                </q-avatar>
                <div class="font-7-8r q-ml-sm">{{ tabs[t].shortName }}</div>
              </div>
            </q-tab>
          </q-tabs>
        </div>
        <q-slide-transition>
          <div class="__c __t" v-if="selectedProvider?._id">
            <div class="__title">Selected Profile</div>
            <provider-item class="q-mt-md" :model-value="selectedProvider">
              <template v-slot:side>
                <q-btn dense flat size="sm" color="red" icon="mdi-close" @click="clearSelected"></q-btn>
              </template>
            </provider-item>
          </div>
        </q-slide-transition>
        <div class="__c">
          <div class="__title">{{ tabs[tab].label }}</div>
          <q-tab-panels class="_panel" v-model="tab" animated>
            <q-tab-panel class="_panel" name="org">

              <q-tabs v-if="inOrgs.length" no-caps indicator-color="secondary" align="left" v-model="orgMode">
                <q-tab name="edit">
                  <span class="font-3-4r">Choose Org</span>
                </q-tab>
                <q-tab name="add">
                  <span class="font-3-4r">Add New</span>
                </q-tab>
              </q-tabs>

              <q-tab-panels class="_panel" v-model="orgMode" animated transition-next="jump-up"
                            transition-prev="jump-down">
                <q-tab-panel class="_panel" name="edit">
                  <div class="q-pa-md">
                    <org-context-item></org-context-item>
                  </div>
                  <org-form :model-value="org"></org-form>

                </q-tab-panel>
                <q-tab-panel class="_penal" name="add">

                  <org-form @update:model-value="addedOrg"></org-form>

                </q-tab-panel>
              </q-tab-panels>

            </q-tab-panel>
            <q-tab-panel class="_panel" name="search">

              <div class="q-pa-sm font-7-8r">
                We aggregate several public sources to let users search providers. You can claim unclaimed accounts so you control your full search results on CommonCare.
              </div>
              <provider-search
                  stack
                  :query="{ org: { $exists: false }}"
                  v-model="selectedProvider"
                  @update:model-value="addProvider"
              ></provider-search>

            </q-tab-panel>
            <q-tab-panel class="_panel" name="provider">

              <provider-form
                  v-if="!resetting"
                  @update:model-value="addProvider"
                  :org="org"
                  :model-value="selectedProvider"
              ></provider-form>

            </q-tab-panel>
          </q-tab-panels>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import ProviderSearch from 'components/providers/forms/ProviderSearch.vue';
  import ProviderForm from 'components/providers/forms/ProviderForm.vue';
  import OrgForm from 'components/orgs/forms/OrgForm.vue';
  import OrgContextItem from 'components/orgs/utils/OrgContextItem.vue';
  import ProviderItem from 'components/providers/cards/ProviderItem.vue';

  import {computed, ref, watch} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {loginPerson} from 'stores/utils/login';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';
  const { person } = loginPerson()

  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const orgStore = useOrgs();

  const selectedProvider = ref();
  const orgMode = ref('edit')

  const { item: org } = idGet({
    store: orgStore,
    value: getOrgId
  })

  const tab = ref('org');
  const addProvider = (val) => {
    if (val?._id) tab.value = 'provider'
  }
  const tabs = ref({
    'org': {
      label: 'Your Organization',
      shortName: 'Org'
    },
    'search': {
      label: 'Search Providers',
      shortName: 'Match'
    },
    'provider': {
      label: 'Provider Profile',
      shortName: 'Profile'
    }
  });

  const inOrgs = computed(() => (person.value?.inOrgs || []).filter(a => !!a))

  const addedOrg = (o) => {
    envStore.setOrgId(o._id)
    orgMode.value = 'edit'
  }
  const resetting = ref(false);
  const clearSelected = () => {
    resetting.value = true;
    selectedProvider.value = undefined;
    setTimeout(() => {
      resetting.value = false;
    }, 50)
  }

  watch(inOrgs, (nv) => {
    if (nv?.length) {
      orgMode.value = 'edit'
    } else orgMode.value = 'add'
  }, { immediate: true })

</script>

<style lang="scss" scoped>
  .__c {
    padding: 40px min(15px, 2vw) 20px min(15px, 2vw);
    border-radius: 20px;
    box-shadow: 0 2px 18px -6px #999;
    background: white;
    margin: 30px 0;
    position: relative;

    .__title {
      position: absolute;
      top: 0;
      left: 2.5%;
      width: 95%;
      transform: translate(0, -30%);
      border-radius: 6px;
      background: linear-gradient(-184deg, var(--q-s1), transparent);
      //box-shadow: 0 2px 2px rgba(0,0,0,.1);
      //background: linear-gradient(180deg, var(--q-s1), white);
      color: var(--q-s9);
      font-weight: 600;
      padding: 6px 8px;
      font-size: 1rem;
    }
  }
  .__t {
    padding: 15px;
  }
</style>
