<template>
  <div class="_fw __pd">
    <div class="row">
      <div class="col-12 q-pa-sm">
        <bank-account-table :org="fullOrg"></bank-account-table>

      </div>
      <div class="col-12 q-pa-sm">

      </div>
    </div>

  </div>
</template>

<script setup>
  import BankAccountTable from 'components/accounts/lists/BankAccountTable.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useOrgs} from 'stores/orgs';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';
  const store = useOrgs();
  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const props = defineProps({
    org: { required: true }
  })

  const { item: fullOrg } = idGet({
    store,
    value: computed(() => props.org || getOrgId.value)
  })



</script>

<style lang="scss" scoped>
  .__pd {
    padding: 10px min(3vw, 20px)
  }
</style>
