<template>
  <div class="_fw">
    <connected-account-banner :org="fullOrg"></connected-account-banner>
    <template v-if="account?.accountID">
      <div class="row items-center">
        <credit-btn
            flat
            no-caps
            :org="fullOrg"
            :moov_account="account"
            :care_account="care_account">
          <span class="q-mr-sm">Add Funds</span>
          <q-icon name="mdi-plus" color="primary"></q-icon>
        </credit-btn>
        <q-separator vertical class="q-mx-sm"></q-separator>
        <debit-btn
            flat no-caps
            :org="fullOrg"
            :care_account="care_account"
            :moov_account="account">
          <span class="q-mr-sm">Send Funds</span>
          <q-icon name="mdi-chevron-right" color="secondary"></q-icon>
        </debit-btn>

      </div>
      <q-separator class="q-my-md"></q-separator>
      <div class="row">
        <div class="col-12 col-md-6 q-pa-sm">
          <table>
            <tbody>
            <tr>
              <td>Account Balance</td>
              <td class="__balance text-black">
                {{ dollarString(balances.cash / 100, '$', 2) }}
              </td>
            </tr>
            <tr>
              <td>Outbound</td>
              <td class="__balance text-s6">{{ dollarString(balances.outbound_pending / 100, '$', 2) }}</td>
            </tr>
            <tr>
              <td>Inbound</td>
              <td class="__balance text-p6">{{ dollarString(balances.inbound_pending / 100, '$', 2) }}</td>
            </tr>
            <tr>
              <td>Pending Balance</td>
              <td class="__balance text-grey-6">{{ dollarString(balances.balance / 100, '$', '2') }}</td>
            </tr>
            </tbody>
          </table>
        </div>

      </div>
      <q-separator class="q-my-md" color="p2"></q-separator>
      <account-transactions :moov_account="account" :model-value="care_account"></account-transactions>
    </template>
    <div v-else class="q-pa-lg">
      <q-spinner color="primary" size="30px"></q-spinner>
    </div>

  </div>
</template>

<script setup>
  import CreditBtn from 'components/accounts/treasury/components/cards/CreditBtn.vue';
  import AccountTransactions from 'components/accounts/treasury/transactions/AccountTransactions.vue';
  import DebitBtn from 'components/accounts/treasury/components/cards/DebitBtn.vue';
  import ConnectedAccountBanner from 'components/accounts/treasury/components/cards/ConnectedAccountBanner.vue';

  import {computed, ref, watch} from 'vue';
  import {dollarString} from 'src/utils/global-methods';

  import {idGet} from 'src/utils/id-get';
  import {useCareAccounts} from 'stores/care-accounts';
  import {useOrgs} from 'stores/orgs';
  import {useBanking} from 'stores/banking';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const caStore = useCareAccounts();
  const orgStore = useOrgs();
  const bankStore = useBanking();

  const props = defineProps({
    modelValue: { required: true },
    org: { required: true }
  })

  const { item: care_account } = idGet({
    store: caStore,
    value: computed(() => props.modelValue),
    params: ref({ runJoin: { with_wallet: true } }),
    refreshWhen: computed(() => !props.modelValue?._fastjoin?.account)
  })
  const { item: fullOrg } = idGet({
    store: orgStore,
    value: computed(() => care_account.value?.owner || getOrgId.value)
  })
  const account = computed(() => care_account.value?._fastjoin?.account);
  const wallet = computed(() => care_account.value?._fastjoin?.wallet);

  const pendingTransactions = ref([])

  watch(wallet, async (nv, ov) => {
    if (nv && nv.walletID !== ov?.walletID) {
      const res = await bankStore.get(account.value.accountID, {
        banking: {
          moov: {
            method: 'get_wallet_transactions',
            args: [account.value.accountID, wallet.value.walletID || account.value.walletID, { status: 'pending' }]
          }
        }
      })
      if (res?.transactions) pendingTransactions.value = res.transactions
    }
  }, { immediate: true })

  const balances = computed(() => {
    const pendingBalance = pendingTransactions.value?.reduce((acc, a) => acc + a.netAmount || 0, 0) || 0
    const { value } = wallet.value?.availableBalance || {};
    const obj = {
      cash: value,
      inbound_pending: pendingBalance,
      outbound_pending: 0,
    };
    obj.balance = (obj.cash || 0) - obj.outbound_pending + obj.inbound_pending
    return obj;
  })

</script>

<style lang="scss" scoped>
  .__balance {
    padding: 6px 12px;
    border-radius: 5px;
    font-weight: 600;
    width: auto;
    font-size: 1.1rem;
    transform: translate(0, 5px);
    text-align: right;
  }

  table {
    border-collapse: collapse;
    width: 100%;

    td {
      padding: 6px 12px;
      text-align: left;
    }

    tr {
      td {
        border-bottom: dotted 2px #999;
      }

      td:first-child {
        font-size: .8rem;
        text-transform: uppercase;
        font-weight: 500;

      }
    }
  }

</style>
