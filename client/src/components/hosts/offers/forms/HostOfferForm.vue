<template>
  <div class="_fw">
    <div class="q-pa-sm font-1r tw-six">
      Service Offering
    </div>
    <q-tab-panels class="_panel" :model-value="!choosingPlan" animated>
      <q-tab-panel class="_panel" :name="true">
        <div class="_form_grid q-py-md">
          <div class="_form_label">Company</div>
          <div class="q-pa-sm">
            <host-chip :model-value="host"></host-chip>
          </div>
          <div class="_form_label">Role</div>
          <div class="q-pa-sm">
            <role-chip :role-list="host.roles" picker v-model="form.role" @update:model-value="autoSave('role')"></role-chip>
          </div>
          <template v-if="form.role">
            <div class="_form_label">Plan</div>
            <div class="q-pa-sm">
              <q-chip color="ir-grey-2" clickable @click="choosingPlan = true">
                <span class="tw-six" v-if="form?.plan">{{ plan?.name }}</span>
                <span v-else>Select Plan</span>
              </q-chip>
            </div>
            <div class="_form_label">Fee</div>
            <div class="q-pa-sm">
              <fee-chip picker :model-value="form" @update:fee="setForm('fee', $event)" @update:fee-type="setForm('feeType', $event)"></fee-chip>
            </div>
            <div class="_form_label">Contract</div>
            <div class="q-pa-sm">
              <q-chip color="ir-grey-2">
                <q-icon class="q-mr-sm" :color="form.contract ? 'a4' : 'a2'" name="mdi-file-document"></q-icon>
                <span v-if="contract?._id">{{ contract?.name }}</span>
                <span v-else>Select Contract</span>
                <q-icon class="q-ml-sm" name="mdi-menu-down"></q-icon>

                <q-menu>
                  <div class="w300 mw100 q-pa-sm bg-white">
                    <q-input v-model="cSearch.text" dense filled>
                      <template v-slot:prepend>
                        <q-icon name="mdi-magnify"></q-icon>
                      </template>
                    </q-input>
                    <q-list separator>
                      <q-item v-for="(c, i) in c$.data" :key="`c-${i}`" clickable @click="setForm('contract', c._id)">
                        <q-item-section avatar>
                          <q-icon color="a2" name="mdi-file-document" size="20px"></q-icon>
                        </q-item-section>
                        <q-item-section>
                          <q-item-label>{{c.name}}</q-item-label>
                        </q-item-section>
                      </q-item>
                    </q-list>
                  </div>
                </q-menu>
              </q-chip>
            </div>
          </template>
        </div>
        <div></div>
        <div class="q-pa-md">
        <div v-if="!form._id && form.role && form.plan && form.fee && form.feeType && form.contract" class="q-pa-md">
          <q-btn class="_a_btn tw-six" no-caps label="Send Offer" push @click="save"></q-btn>
        </div>
        <div v-else-if="form._id && Object.keys(patchObj || {}).length">
          <q-btn class="_a_btn tw-six" no-caps label="Update Offer" push @click="update"></q-btn>
        </div>
        </div>

      </q-tab-panel>
      <q-tab-panel class="_panel" :name="false">

        <div class="q-px-md _fw">
          <div class="_fw q-pb-sm">
            <q-btn dense flat icon="mdi-chevron-left" color="primary" @click="choosingPlan = false"></q-btn>
          </div>
          <div class="q-pb-sm tw-six font-3-4r">Plans you can bid on as {{roles[form.role]?.label}}</div>

          <q-radio v-model="publicPlans" :val="false" :label="`Plans who allow ${host?.dba || 'you'} to bid`"></q-radio>
          <q-radio v-model="publicPlans" :val="true" label="Open to public bid"></q-radio>
          <q-input dense filled v-model="search.text">
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>

          <q-list separator>
            <q-item v-for="(p, i) in p$.data" :key="`p-${i}`" clickable @click="setPlan(p)">
              <q-item-section>
                <q-item-label class="tw-six">{{p.name}}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>

        </div>

      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>

  import RoleChip from 'components/plans/team/cards/RoleChip.vue';
  import HostChip from 'components/hosts/cards/HostChip.vue';
  import FeeChip from 'components/plans/team/cards/FeeChip.vue';

  import {computed, watch, ref} from 'vue';
  import {usePlans} from 'stores/plans';
  import {HFind} from 'src/utils/hFind';
  import {useHosts} from 'stores/hosts';
  import {HQuery} from 'src/utils/hQuery';
  import {idGet} from 'src/utils/id-get';
  import {LocalStorage} from 'symbol-auth-client';
  import {HForm, HSave} from 'src/utils/hForm';
  import {useOffers} from 'stores/offers';
  import { roles} from 'components/plans/team/utils/roles';
  import {useContracts} from 'stores/contracts';
  import {useEnvStore} from 'stores/env';

  const planStore = usePlans();
  const hostStore = useHosts();
  const offerStore = useOffers();
  const contractStore = useContracts();
  const envStore = useEnvStore();

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    planId: { required: false },
    role: String,
    modelValue: { required: false }
  })

  const choosingPlan = ref(false);

  const { item: host } = idGet({
    store: hostStore,
    value: computed(() => LocalStorage.getItem('host_id'))
  })

  const formFn = (defs) => {
    return {
      plan: props.planId,
      role: props.role,
      ...defs
    }
  }
  const { form, save } = HForm({
    store: offerStore,
    successMessage: 'Offer Sent',
    beforeFn: (val) => {
      return {
        host: host.value._id,
        ...val
      }
    },
    afterFn: (val) => {
      emit('update:model-value', val);
    },
    value: computed(() => props.modelValue),
    formFn
  })

  const { autoSave, setForm, patchObj } = HSave({
    form,
    store: offerStore,
    save,
    pause: ref(true)
  })

  const update = () => {
    if(Object.keys(patchObj.value).length) offerStore.patch(form.value._id, patchObj.value)
  }

  const setPlan = (p) => {
    form.value.plan = p._id;
    choosingPlan.value = false;
  }

  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => form.value?.plan)
  })
  const { item:contract } = idGet({
    store: contractStore,
    value: computed(() => form.value?.contract)
  })

  watch(() => props.modelValue, (nv) => {
    if (nv) form.value = formFn(nv);
  }, { immediate: true })

  const publicPlans = ref(false);

  const { search, searchQ } = HQuery({})
  const params = computed(() => {
    const query = { ...searchQ.value };
    if(publicPlans.value) query[`rfp.${form.value.role}.public`] = true;
    else query[`rfp.${form.value.role}.hosts`] = { $in: [host.value._id] };
    return {
      query
    }
  })
  const { h$:p$ } = HFind({
    store: planStore,
    name: 'host-offer',
    log: false,
    pause: computed(() => !choosingPlan.value),
    limit: ref(10),
    params
  })

  const { search:cSearch, searchQ:searchC } = HQuery({})
  const { h$:c$ } = HFind({
    store: contractStore,
    params: computed(() => {
      return {
        query: {
          ...searchC.value,
          owner: envStore.getOrgId
        }
      }
    })
  })
</script>

<style lang="scss" scoped>

</style>
