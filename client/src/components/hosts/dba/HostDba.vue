<template>
  <div class="_fw">
    <div class="q-py-lg q-px-md">
      <div class="row items-center">
        <div class="font-1r tw-six">DBA Settings</div>
        <q-space></q-space>
        <q-btn flat size="sm" @click="addDialog = true">
          <q-icon name="mdi-plus" color="accent" class="q-mr-sm"></q-icon>
          <span class="tw-six">New DBA</span>
        </q-btn>
      </div>
      <div class="font-7-8r">Configure your dba for how groups and providers experience your plan guide services</div>
    </div>

    <div class="_fw q-pb-lg">
      <host-form :model-value="host"></host-form>
    </div>

    <common-dialog setting="right" v-model="addDialog">
      <div class="_fw q-pa-md bg-white">
        <div class="q-pa-md">
          <div class="tw-six font-1r">Add DBA</div>
        </div>
        <host-form :org-id="org._id"></host-form>
      </div>
    </common-dialog>

  </div>
</template>

<script setup>
  import HostForm from 'components/hosts/forms/HostForm.vue';

  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {computed, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {useHosts} from 'stores/hosts';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import {useEnvStore} from 'stores/env';
  import { contextItems } from 'layouts/utils/context-items';

  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const orgStore = useOrgs();
  const hostStore = useHosts();

  const addDialog = ref(false);

  const { item: org } = idGet({
    store: orgStore,
    value: getOrgId
  })

  const { item: host } = idGet({
    store: hostStore,
    value: computed(() => LocalStorage.getItem('host_id'))
  })
</script>

<style lang="scss" scoped>

</style>
