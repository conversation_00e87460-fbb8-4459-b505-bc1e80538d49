<template>
  <div class="_fw">
    <file-object-form v-model="form.files" @update:model-value="handleFile">
      <template v-slot:drop="scope">
        <upload-ui height="100px" :allow-types="scope.allowTypes" :upload="scope.upload">
          <template v-slot:display>
            <div class="_fw _fh flex flex-center">
              <div class="tw-six font-3-4r">Click or drop files</div>
            </div>
          </template>
        </upload-ui>
      </template>
    </file-object-form>
  </div>
</template>

<script setup>
  import FileObjectForm from 'components/common/uploads/utils/FileObjectForm.vue';
  import UploadUi from 'components/common/uploads/components/UploadUi.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useVisits} from 'stores/visits';
  import {HForm, HSave} from 'src/utils/hForm';
  import {useClaimReqs} from 'stores/claim-reqs';


  const visitStore = useVisits();
  const crStore = useClaimReqs();

  const props = defineProps({
    visit: { required: false },
    care: { required: false},
    modelValue: { required: false },
    patientId: { required: false },
    personId: { required: false },
    planId: { required: false }
  })
  const { item:cr } = idGet({
    store: crStore,
    value: computed(() => props.modelValue)
  })
  const { item:fullVisit } = idGet({
    store: visitStore,
    value: computed(() => props.visit || cr.value?.visit)
  })

  const formFn = (defs) => {
    return {
      files: {},
      ...defs
    }
  }
  const { form, save } = HForm({
    store: crStore,
    value: cr,
    formFn,
    log: false,
    beforeFn: (val) => {
      const { plan, person, patient, _id, provider } = fullVisit.value || {};
      console.log('returning before', props.planId);
      return {
        plan: plan || props.planId,
        person: person || props.personId,
        patient: patient || props.patientId,
        provider,
        visit: _id,
        ...val
      }
    }
  })

  const { autoSave } = HSave({ form, store: crStore })

  const handleFile = (val) => {
    if(form.value._id) autoSave('files', val);
    else save()
  }
</script>

<style lang="scss" scoped>

</style>
