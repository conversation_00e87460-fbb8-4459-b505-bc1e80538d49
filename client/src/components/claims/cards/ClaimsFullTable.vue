<template>
  <div class="_fw pd3">
    <slot name="filters">
      <div class="row items-center q-pb-sm">
        <q-icon class="q-mr-sm" size="25px" color="primary" name="mdi-filter"></q-icon>
        <member-chip empty-label="Select Person" icon-right="mdi-menu-down" :model-value="selectedPerson">
          <template v-slot:menu>
            <q-menu v-model="menu">
              <div class="w300 mw100 q-pa-md bg-white">
                <q-list>
                  <member-item clickable @update:model-value="selectedPerson = m" v-for="(m, i) in [person, ...m$.data]"
                               :key="`mem-${i}`" :model-value="m"></member-item>
                </q-list>
              </div>
            </q-menu>
          </template>
        </member-chip>
        <q-btn flat dense size="sm" v-if="selectedPerson" color="red" icon="mdi-close"
               @click="selectedPerson = undefined"></q-btn>
      </div>
      <div class="row items-center">
        <div class="col-12 col-md-4">
          <div class="row items-center no-wrap __os">
            <inline-date class="q-mx-xs" :input-attrs="{ dense: true, filled: true, label: 'From Date' }"
                         v-model="dates.from">
              <template v-slot:prepend>
                <q-icon size="15px" color="primary" name="mdi-calendar"></q-icon>
              </template>
              <template v-slot:append>
                <q-btn dense flat size="sm" v-if="dates.from" color="red" icon="mdi-close"
                       @click="dates.from = undefined"></q-btn>
              </template>
            </inline-date>
            <inline-date class="q-mx-xs" :input-attrs="{ dense: true, filled: true, label: 'To Date' }"
                         v-model="dates.to">
              <template v-slot:append>
                <q-btn dense flat size="sm" v-if="dates.to" color="red" icon="mdi-close"
                       @click="dates.to = undefined"></q-btn>
              </template>
            </inline-date>
          </div>
        </div>
        <q-space></q-space>
        <q-chip color="transparent" clickable @click="sort = sort * -1">
          <q-icon color="accent" :name="`mdi-sort-calendar-${sort === 1 ? 'descending' : 'ascending'}`"></q-icon>
          <span class="q-ml-sm font-3-4r">{{ sort === 1 ? 'Oldest' : 'Newest' }}</span>
        </q-chip>
      </div>
    </slot>


    <div class="_fw q-py-md">
      <q-table
          flat
          :rows="c$.data"
          :columns="cols"
      >
        <template v-slot:no-data>
          <div class="q-pa-sm text-italic">No claims</div>
        </template>
        <template v-slot:header="scope">
          <!--        <q-th auto-width></q-th>-->
          <slot name="header-left" v-bind="scope"></slot>
          <q-th
              v-for="col in scope.cols"
              :key="col.name"
              :props="scope"
          >
            {{ col.label }}
          </q-th>
          <slot name="header-right" v-bind="scope"></slot>
        </template>
        <template v-slot:body="scope">
          <q-tr :props="scope" @dblclick="openClaim(scope.row)">
            <template v-if="!loading">
              <slot name="body-left" v-bind="{row: scope.row}"></slot>

              <q-td v-for="(col, i) in scope.cols" :key="`td-${i}`">
                <component
                    v-if="col.component"
                    :is="col.component"
                    v-bind="col.attrs(scope.row)"
                ></component>
                <div v-else>{{ col.value }}</div>
              </q-td>
              <slot name="body-right" v-bind="{row: scope.row}"></slot>
            </template>
            <q-td v-else class="q-pa-md">
              <q-spinner color="grey-3" size="20px"></q-spinner>
            </q-td>
          </q-tr>
        </template>
        <template v-slot:bottom>
          <div class="row items-center _fw">
            <div class="font-7-8r text-grey-8">{{ c$.data?.length ? 1 : 0 }} - {{ c$.data?.length || 0 }} of
              {{ c$.total }}
            </div>
            <q-space></q-space>
            <q-pagination
                @update:model-value="c$.toPage($event)"
                :model-value="pagination.currentPage"
                :min="1"
                :max="pagination.pageCount"
                direction-links
                boundary-numbers
            ></q-pagination>
          </div>
        </template>
      </q-table>
    </div>

  </div>
</template>

<script setup>
  import StatusChip from 'components/care/cards/StatusChip.vue';
  import TdText from 'components/common/tables/TdText.vue';
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import ClaimBalance from 'components/claims/cards/ClaimBalance.vue';
  import InlineDate from 'components/common/dates/InlineDate.vue';
  import MemberChip from 'components/households/cards/MemberChip.vue';
  import MemberItem from 'components/households/cards/MemberItem.vue';

  import {computed, onMounted, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {loginPerson} from 'stores/utils/login';
  import {idGet} from 'src/utils/id-get';
  import {useHouseholds} from 'stores/households';
  import {usePlans} from 'stores/plans';
  import {HFind} from 'src/utils/hFind';
  import {_flatten} from 'symbol-syntax-utils';
  import {useProviders} from 'stores/providers';
  import {useClaims} from 'stores/claims';
  const { person } = loginPerson()

  import {formatDate} from 'src/utils/date-utils';
  import {usePpls} from 'stores/ppls';
  import {useRouter} from 'vue-router';
  import {useEnvStore} from 'stores/env';

  const router = useRouter();

  const planStore = usePlans();
  const providerStore = useProviders();
  const claimStore = useClaims();
  const pplStore = usePpls();
  const envStore = useEnvStore();

  const props = defineProps({
    plan: { required: false },
    household: { required: false },
    openFn: Function,
    query: Object
  })

  const menu = ref(false);
  const openClaim = (c) => {
    if (props.openFn) return props.openFn(c);
    const { href } = router.resolve({ name: 'visit-page', params: { visitId: c.visit } })
    window.open(href, '_blank')
  }

  const hhStore = useHouseholds();

  const { item: hh } = idGet({
    store: hhStore,
    value: computed(() => person.value?.household)
  })
  const hhMembers = computed(() => [...Object.keys(hh.value?.members || {}), hh.value?.person].filter(a => !!a))
  const { h$: m$ } = HFind({
    store: pplStore,
    pause: computed(() => hh.value?._id),
    params: computed(() => {
      return {
        query: {
          _id: { $in: hhMembers.value }
        }
      }
    })
  })

  const dates = ref({
    from: undefined,
    to: undefined
  })

  const { item: fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan || envStore.getPlanId)
  })

  const selectedPerson = ref();
  const sort = ref(-1);
  const params = computed(() => {
    const q = {
      $sort: { date: sort.value },
      ...props.query
    }
    if (!props.query) {
      q.plan = fullPlan.value?._id
      q.patient = { $in: hhMembers.value }
      if (dates.value.from) q.date = { $gte: dates.value.from }
      if (dates.value.to) q.date = { ...q.date, $lt: dates.value.to }
      if (selectedPerson.value) q.patient = selectedPerson.value._id
    }
    return {
      query: q
    }
  })
  const { h$: c$, pagination } = HFind({
    store: claimStore,
    limit: ref(10),
    pause: computed(() => !fullPlan.value),
    params
  })

  const { h$: pv$ } = HFind({
    store: providerStore,
    pause: computed(() => !c$.data?.length),
    params: computed(() => {
      return {
        query: {
          _id: { $in: _flatten((c$.data || []).map(a => a.providers)) }
        }
      }
    })
  })

  const cols = computed(() => {
    return [
      {
        name: 'Date',
        component: TdText,
        attrs: (row) => {
          return {
            col: {
              value: formatDate(row.date, 'MM-DD-YYYY')
            }
          }
        }
      },
      {
        name: 'Patient',
        component: MemberChip,
        attrs: (row) => {
          return {
            modelValue: row.patient,
            color: 'transparent'
          }
        }
      },
      {
        name: 'Balance',
        component: ClaimBalance,
        attrs: (row) => {
          return {
            modelValue: row,
            dense: true
          }
        }
      },
      {
        name: 'Status',
        component: StatusChip,
        attrs: (row) => {
          return {
            label: '',
            modelValue: row.status
          }
        }
      },
      // {
      //   name: 'Treatment',
      //   component: ProcedureOrMed,
      //   attrs: (row) => {
      //     return {
      //       limit: 25,
      //       claim: row
      //     }
      //   }
      // },
      {
        name: 'Provider',
        component: ProviderChip,
        attrs: (row) => {
          return {
            limit: 30,
            chipAttrs: { color: 'transparent' },
            store: providerStore,
            modelValue: row.provider,
          }
        }
      }
    ].map(a => {
      return {
        ...a,
        label: a.name,
        sortable: true,
        align: 'left',
        field: a.name
      };
    });
  })

  const loading = ref(true);
  const unload = (tries = 0) => {
    if (pv$.total) loading.value = false;
    if (tries < 5) {
      setTimeout(() => {
        unload(tries + 1)
      }, 200)
    } else loading.value = false
  }
  onMounted(() => {
    unload()
  })

</script>

<style lang="scss" scoped>
  .__os {
    width: 100%;
    overflow-x: scroll;
  }
</style>
