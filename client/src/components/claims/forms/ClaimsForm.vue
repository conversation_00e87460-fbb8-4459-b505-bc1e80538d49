<template>
  <div class="_fw">
    <template v-if="can.edit">
      <slot name="top">
        <div class="row items-center">
          <div class="q-pa-sm tw-six text-grey-7">{{ form._id ? 'Edit' : 'Add' }} Line Item</div>
          <q-space></q-space>
          <remove-proxy-btn v-show="can.delete" name="Claim" v-if="form._id" @remove="removeClaim"></remove-proxy-btn>
          <q-tabs dense no-caps indicator-color="primary" v-else align="right" v-model="tab">
            <q-tab name="form">
              <span class="font-3-4r tw-six">Enter Details</span>
            </q-tab>
            <q-tab name="upload">
              <span class="font-3-4r tw-six">Upload Bill</span>
            </q-tab>
          </q-tabs>
        </div>
        <q-separator class="q-my-xs"></q-separator>
      </slot>

      <q-tab-panels class="_panel" v-model="tab" animated>
        <q-tab-panel class="_panel" name="form">

          <div class="__t">
            <div>Seen By</div>
          </div>
          <div>
            <practitioner-chip
                :hide-avatar="!practitioner?._id"
                :model-value="practitioner || { displayName: 'Choose Practitioner', _id: '1' }"
                default-name="Choose Practitioner"
            >
              <template v-slot:menu>
                <q-menu>
                  <div class="__m500 _fw">
                    <q-list separator>
                      <q-item-label header>Choose Practitioner</q-item-label>
                      <add-practitioner-item emit-value v-model="form.practitioner"
                                             @update:model-value="setDoc"></add-practitioner-item>
                      <practitioner-item
                          v-for="k in hh?.practitioners || []"
                          :key="k"
                          :model-value="k"
                          clickable
                          @click="setPractitioner(k)"
                      >
                      </practitioner-item>
                    </q-list>
                  </div>
                </q-menu>
              </template>
              <template v-slot:right>
                <q-icon name="mdi-menu-down" v-if="!form.practitioner"></q-icon>
                <q-btn dense flat size="sm" name="mdi-close" color="red" @click="unsetPractitioner"
                       v-else></q-btn>
              </template>
            </practitioner-chip>
          </div>
          <div class="__t">
            <div>Billed Item</div>
          </div>
          <div class="q-pa-sm">
            <q-tab-panels animated class="_panel" transition-prev="slide-up" transition-next="slide-down"
                          :model-value="(!form.procedure && !form.med && !form.misc) ? 'set' : 'show'">
              <q-tab-panel class="_panel" name="set">
                <div class="row items-center" v-if="!form.procedure && !form.med">
                  <template v-for="(tb, i) in tabs" :key="`tch-${i}`">
                    <q-chip color="transparent" dense square clickable @click="addTab = tb.name">
                      <span :class="addTab === tb.name ? 'tw-six text-p9' : 'text-p12'">{{ tb.label }}</span>
                    </q-chip>
                    <div v-if="i < tabs.length - 1">|</div>
                  </template>
                </div>
                <q-tab-panels
                    v-if="!form.procedure && !form.med"
                    class="_panel"
                    v-model="addTab"
                    animated
                >
                  <q-tab-panel class="_panel" name="procedure">
                    <procedure-autocomplete
                        outlined
                        @update:model-value="setProcedure"
                    ></procedure-autocomplete>
                  </q-tab-panel>
                  <q-tab-panel class="_panel" name="med">
                    <meds-autocomplete outlined @update:model-value="setMed"></meds-autocomplete>
                  </q-tab-panel>
                  <q-tab-panel class="_panel" name="meta">
                    <div class="q-pa-sm font-3-4r">Enter charge detail here only if a procedure or drug code could not
                      be
                      found
                      for it
                    </div>
                    <q-input outlined autogrow label="Claim Description" v-model="form.misc"></q-input>
                  </q-tab-panel>
                </q-tab-panels>
              </q-tab-panel>
              <q-tab-panel class="_panel" name="show">
                <procedure-item v-if="form.procedure" :model-value="form.procedure">
                  <template v-slot:side>
                    <q-item-section side>
                      <remove-proxy-btn icon="mdi-close" name="procedure"
                                    @remove="unsetProcedure"></remove-proxy-btn>
                    </q-item-section>
                  </template>
                </procedure-item>
                <med-item v-if="form.med" :model-value="form.med">
                  <template v-slot:side>
                    <q-item-section side>
                      <remove-proxy-btn name="med" @remove="unsetMed"></remove-proxy-btn>
                    </q-item-section>
                  </template>
                </med-item>
              </q-tab-panel>
            </q-tab-panels>
          </div>

          <div class="_form_grid" v-if="!!(form.procedure || form.med || form.misc)">
            <div class="_form_label">Price Per Unit</div>
            <div class="q-pa-sm">
              <money-input
                  dense
                  filled
                  @update:model-value="setAmount($event * 100)"
                  :model-value="(form.amount || 0) / 100" label="Price Per Unit"></money-input>
            </div>
            <div class="_form_label">Units</div>
            <div class="q-pa-sm">
              <q-input dense filled type="number" :model-value="form.qty"
                       @update:model-value="setQty(Number($event))" label="Units"></q-input>
            </div>
            <div class="_form_label">Notes</div>
            <div class="q-pa-sm">
              <q-input dense filled autogrow v-model="form.notes" @update:model-value="autoSave('notes')"
                       label="Notes"></q-input>

            </div>
            <div class="_form_label">Taxes</div>
            <div class="q-px-sm">
              <q-chip color="white" label="Add Taxes" clickable class="text-blue" @click="addTax"></q-chip>

              <div class="row items-center no-wrap q-py-xs" v-for="(tax, i) in Object.keys(form.taxes || {})"
                   :key="`tax-${i}`">
                <q-input @update:model-value="setTaxKey(tax, $event)" @blur="finishTax(tax)" dense hide-bottom-space
                         v-model="form.taxes[tax].name" placeholder="Tax Name"></q-input>
                <div class="q-px-sm">:</div>
                <div class="__m50">
                  <money-input
                      dense
                      filled
                      hide-bottom-space
                      :model-value="(form.taxes[tax].amount||0)/100"
                      @update:model-value="setTaxAmount(tax, $event * 100)"
                      placeholder="Amount"
                  ></money-input>
                </div>
                <remove-proxy-btn icon="mdi-close" name="tax" @remove="unsetTax(tax)"></remove-proxy-btn>
              </div>
            </div>
            <div class="_form_label">Fees</div>
            <div class="q-px-sm">
              <q-chip color="white" label="Add Fees" clickable class="text-blue" @click="addFee"></q-chip>

              <div class="row items-center no-wrap q-py-xs" v-for="(fee, i) in Object.keys(form.fees || {})"
                   :key="`tax-${i}`">
                <q-input
                    @update:model-value="setFeeKey(fee, $event)"
                    @blur="finishFee(fee)"
                    dense hide-bottom-space
                    v-model="form.fees[fee].name"
                    placeholder="Tax Name"
                ></q-input>
                <div class="q-px-sm">:</div>
                <div class="__m50">
                  <money-input
                      dense
                      filled
                      hide-bottom-space
                      :model-value="(form.fees[fee].amount || 0)/100"
                      @update:model-value="setFeeAmount(fee,$event * 100)"
                      placeholder="Amount"
                  ></money-input>
                </div>
                <remove-proxy-btn icon="mdi-close" name="tax" @remove="unsetFee(fee)"></remove-proxy-btn>
              </div>
            </div>

          </div>

        </q-tab-panel>
        <q-tab-panel class="_panel" name="upload">
          <claim-request-form :visit="fullVisit"></claim-request-form>
        </q-tab-panel>
      </q-tab-panels>


      <div class="q-py-md row items-center">
        <q-btn color="primary" glossy rounded push label="Save Bill" icon-right="mdi-check"
               @click="form._id ? maybeSave() : save()"></q-btn>
      </div>
    </template>
    <template v-else>
      <div class="q-pa-md text-italic font-1r">
        You don't have ability to edit this claim
      </div>
    </template>
  </div>
</template>

<script setup>
  import PractitionerChip from 'components/practitioners/cards/PractitionerChip.vue';
  import PractitionerItem from 'components/practitioners/cards/PractitionerItem.vue';
  import ProcedureAutocomplete from 'components/care/procedures/lists/ProcedureAutocomplete.vue';
  import MedsAutocomplete from 'components/care/meds/lists/MedsAutocomplete.vue';
  import ProcedureItem from 'components/care/procedures/cards/ProcedureItem.vue';
  import MedItem from 'components/care/meds/cards/MedItem.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import AddPractitionerItem from 'components/practitioners/cards/AddPractitionerItem.vue';
  import ClaimRequestForm from 'components/claims/claim-requests/forms/ClaimRequestForm.vue';

  import {HForm, HSave} from 'src/utils/hForm';
  import {useClaims} from 'stores/claims';
  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useVisits} from 'stores/visits';
  import {usePractitioners} from 'stores/practitioners';

  import {objKeyEdit} from 'components/common/uploads/utils';
  import {$errNotify, $successNotify} from 'src/utils/global-methods';
  import {usePpls} from 'stores/ppls';
  import {useHouseholds} from 'stores/households';
  import {loginPerson} from 'stores/utils/login';
  const { login } = loginPerson()

  const claimsStore = useClaims();
  const visitStore = useVisits();
  const pStore = usePractitioners();
  const pplStore = usePpls();
  const hhStore = useHouseholds();

  const emit = defineEmits(['update:model-value', 'update:practitioner', 'close'])
  const props = defineProps({
    modelValue: { required: false },
    visit: { required: true },
    practitioner: { required: false }
  })

  const tab = ref('form')

  const { item: claim } = idGet({
    store: claimsStore,
    value: computed(() => props.modelValue)
  })

  const { item: fullVisit } = idGet({
    store: visitStore,
    value: computed(() => props.visit)
  })

  const { item: person } = idGet({
    store: pplStore,
    value: computed(() => fullVisit.value?.person)
  })
  const { item: hh } = idGet({
    store: hhStore,
    value: computed(() => person.value?.household)
  })

  const addTab = ref('procedure');

  const tabs = computed(() => [
    {
      name: 'procedure',
      label: 'Services'
    },
    {
      name: 'med',
      label: 'Meds'
    },
    {
      name: 'meta',
      label: 'Other'
    }
  ])

  const afterFn = (val) => {
    emit('update:model-value', val);
  }
  const { form, save } = HForm({
    store: claimsStore,
    value: claim,
    notify: true,
    beforeFn: (val) => {
      val.date = fullVisit.value.date
      if (!val.visit) val.visit = fullVisit.value._id
      if (!val.patient) val.patient = fullVisit.value.patient
      if (!val.person) val.person = fullVisit.value.person
      if (!val.plan) val.plan = fullVisit.value.plan
      if (!val.provider) val.provider = fullVisit.value.provider
      return val;
    },
    afterFn
  })

  const { autoSave, setForm, patchObj, maybeSave } = HSave({
    form,
    store: claimsStore,
    pause: ref(true),
    emit: afterFn,
    log: false
  })

  const setAmount = (val) => {
    form.value.amount = val;
    autoSave('amount');
  }
  const setQty = (val) => {
    form.value.qty = val;
    autoSave('qty');
  }

  const setDoc = (val) => {
    if (val && form.value?._id) claimsStore.patch(form.value._id, { $set: { practitioner: val } })
  }

  const removeClaim = async () => {
    const c = await claimsStore.remove(form.value._id, { disableSoftDelete: true })
        .catch(err => {
          console.error('Error removing', err.message);
          $errNotify('Error removing claim - try again')
        })
    if (c) {
      emit('close')
      $successNotify('Claim Removed')
    }
  }

  const {
    setKey: setTaxKey,
    finishKey: finishTax
  } = objKeyEdit(computed(() => form.value?.taxes), { emit: (val) => setForm('taxes', val) })
  const {
    setKey: setFeeKey,
    finishKey: finishFee
  } = objKeyEdit(computed(() => form.value?.fees), { emit: (val) => setForm('fees', val) })

  const addTax = () => {
    const key = `Tax-${Object.keys(form.value?.taxes || {}).length + 1}`
    form.value.taxes = { [key]: { name: key, amount: 0 }, ...form.value.taxes }
    autoSave('taxes')
  }
  const setTaxAmount = (key, val) => {
    form.value.taxes[key] = { ...form.value.taxes[key], amount: val }
    autoSave('taxes');
  }
  const unsetTax = (key) => {
    delete form.value.taxes[key]
    patchObj.value.$unset = { ...patchObj.value.$unset, [`taxes.${key}`]: '' }
  }
  const addFee = () => {
    const key = `Fee-${Object.keys(form.value?.fees || {}).length + 1}`
    form.value.fees = { [key]: { name: key, amount: 0 }, ...form.value.fees }
    autoSave('fees')
  }
  const setFeeAmount = (key, val) => {
    form.value.fees[key] = { ...form.value.fees[key], amount: val }
    autoSave('fee');
  }
  const unsetFee = (key) => {
    delete form.value.fees[key]
    patchObj.value.$unset = { ...patchObj.value.$unset, [`fees.${key}`]: '' }
  }
  const unsetPractitioner = () => {
    form.value.practitioner = undefined;
    patchObj.value.$unset = { ...patchObj.value.$unset, practitioner: '' }
  }
  const unsetProcedure = () => {
    form.value.procedure = undefined;
    patchObj.value.$unset = { ...patchObj.value.$unset, procedure: '' }
  }
  const unsetMed = () => {
    form.value.med = undefined;
    patchObj.value.$unset = { ...patchObj.value.$unset, med: '' }
  }
  const setProcedure = (val) => {
    form.value.procedure = val._id;
    unsetMed()
    autoSave('procedure');
  }
  const setMed = (val) => {
    form.value.med = val._id;
    unsetProcedure();
    autoSave('med');
  }

  const setPractitioner = (k) => {
    form.value.practitioner = k;
    autoSave('practitioner');
    emit('update:practitioner', k);
    if (form.value._id) claimsStore.patch(form.value._id, { $set: { practitioner: k } })
    const { practitioners, _id } = fullVisit.value || {}
    if (_id && (!practitioners?.includes(k))) {
      visitStore.patchInStore(_id, { practitioners: [...practitioners || [], k] })
    }
  }

  const { item: practitioner } = idGet({
    value: computed(() => form.value.practitioner),
    store: pStore
  })

  const can = computed(() => {
    const { createdBy, enteredBy, payments } = claim.value || {}
    const hasPayments = payments?.length;
    const loginId = login.value?._id;
    if (!loginId) return { edit: false }
    const edit = createdBy?.login && createdBy.login === loginId || ((!enteredBy?.id && !enteredBy?.org) || enteredBy?.id === loginId)
    const canDelete = edit && !hasPayments
    return { edit, delete: canDelete }
  })

</script>

<style lang="scss" scoped>
  .__bx {
    border-radius: 10px;
    border: solid 1px var(--q-ir-grey-4);
    padding: 20px;
  }

  .__m50 {
    min-width: 50px;
  }

  .__t {
    font-size: .75rem;
    font-weight: 600;
    margin: 10px 0 5px 0;
    display: flex;
    justify-content: flex-start;

    div:first-child {
      padding: 5px 10px;
      border-radius: 3px;
      background: var(--q-p1);
      color: var(--q-p9);
    }
  }
</style>
