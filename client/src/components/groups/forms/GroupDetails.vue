<template>
  <div class="_fw">
    <div class="_fw q-py-sm">
      <div class="_f_g">
        <!--        //Name-->
        <div class="_f_l _f_chip">
          Name
        </div>
        <div class="q-pa-sm">
          <div v-if="!canEdit?.ok" class="font-1-1-4r text-weight-bold">{{ form.name }}</div>
          <q-input
              v-else
              hide-bottom-space
              placeholder="Name this group..."
              dense
              borderless
              input-class="font-1r tw-five"
              v-model="form.name"
              @update:model-value="dirty = true"
          ></q-input>
        </div>

        <div class="_f_l _f_chip">Plan Class</div>
        <div class="q-pa-sm">
          <q-checkbox
              :disable="!canEdit?.ok"
              v-model="form.planClass"
              @update:model-value="dirty = true"
              label="Is a distinct employee class (for benefits)"
          ></q-checkbox>
        </div>

        <div class="_f_l _f_chip">Description</div>
        <div class="q-pa-sm">
          <div v-if="!canEdit?.ok" class="font-1r">{{ form.description }}</div>

          <q-input
              v-else
              autogrow
              placeholder="Describe this group..."
              dense
              borderless
              v-model="form.description"
              @update:model-value="dirty = true"
          ></q-input>
        </div>

        <q-slide-transition>
          <div v-if="dirty" class="row q-py-md">
            <q-btn color="primary" push label="Save" icon-right="mdi-content-save" @click="save"></q-btn>
          </div>
        </q-slide-transition>
      </div>
    </div>

  </div>
</template>

<script setup>
  import {computed, ref} from 'vue';
  import {HForm} from 'src/utils/hForm';
  import {useGroups} from 'src/stores/groups';

  const store = useGroups();
  const props = defineProps({
    modelValue: { required: false },
    canEdit: Object,
    orgId: { required: true }
  })
  const dirty = ref(false);

  const value = computed(() => props.modelValue)
  const { form, save } = HForm({
    store,
    value,
    name: 'GroupForm',
    validate: true,
    beforeFn: (v) => {
      if (!v.org) v.org = props.orgId
      return v;
    },
    afterFn: () => {
      dirty.value = false;
    },
    vOpts: ref({
      'name': { name: 'Name', v: ['notEmpty'] }
    })
  })

</script>

<style lang="scss" scoped>

</style>
