<template>
  <div>
    <div class="q-pa-md font-1r tw-six">Contracts
      <q-btn dense flat icon="mdi-plus" color="accent" class="_i_i" no-caps @click="$router.push({ name: 'edit-contract', params: { type: 'new' }})"></q-btn>
    </div>
    <q-input dense filled class="mw400 q-mt-md" v-model="search.text" placeholder="Find a  template...">
      <template v-slot:prepend>
        <q-icon name="mdi-magnify"></q-icon>
      </template>
    </q-input>

    <div class="row q-py-md">
      <div class="col-12 col-md-3 q-pa-sm" v-for="(c, i) in c$.data" :key="`c-${i}`">
        <div class="__c relative-position" >
          <div class="t-r">
            <q-btn dense flat icon="mdi-dots-vertical" color="accent" size="sm">
              <q-menu>
                <div class="w200 mw100 q-pa-sm bg-white">
                  <q-list separator>
                    <q-item clickable @click="copy(c)">

                      <q-item-section>
                        <q-item-label>Copy Contract</q-item-label>
                      </q-item-section>
                      <q-item-section side>
                        <q-icon name="mdi-content-copy" color="primary"></q-icon>
                      </q-item-section>
                    </q-item>
                    <remove-item name="Contract" @remove="remove(c)"></remove-item>
                  </q-list>
                </div>
              </q-menu>
            </q-btn>
          </div>
          <contract-card @click="setEditing(c)" :model-value="c"></contract-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import ContractCard from 'components/contracts/cards/ContractCard.vue';
  import RemoveItem from 'components/common/buttons/RemoveItem.vue';

  import {useContracts} from 'stores/contracts';
  import {computed, ref} from 'vue';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import {useRouter} from 'vue-router';
  import {copyContract} from 'components/contracts/utils/copy';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const envStore = useEnvStore();
  const { getOrgId:orgId } = contextItems(envStore);

  const contractStore = useContracts();
  const router = useRouter();

  const props = defineProps({
    query: Object
  })


  const subjects = ref(['cams', 'plans'])
  const { search, searchQ } = HQuery({ keys: ['name', 'description'] })
  const { h$: c$ } = HFind({
    store: contractStore,
    params: computed(() => {
      const query = { ...searchQ.value, subjectService: { $in: subjects.value?.length ? subjects.value : ['plans', 'refs']}, owner: orgId.value, ...props.query }
      return {
        query
      }
    })
  })

  const setEditing = (value) => {
    router.push({ name: 'edit-contract', params: { contractId: value._id, type: 'edit' } })
  }
  const copy = async (val) => {
    const copied = copyContract(val);
    await contractStore.create({ ...copied, name: `Copy of ${val.name}` });
  }
  const remove = (val) => {
    contractStore.remove(val._id);
  }
</script>

<style lang="scss" scoped>
  .__c {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 2px 8px -2px #999;
    padding: 30px 20px;
    cursor: pointer;
    transition: all .2s;

    &:hover {
      box-shadow: 0 4px 16px -8px #999;
    }
  }
</style>
