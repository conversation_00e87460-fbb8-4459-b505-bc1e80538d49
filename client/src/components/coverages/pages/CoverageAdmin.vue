<template>
  <q-page class="_fw _bg_ow">
    <div class="row justify-center">
      <div class="_cent bg-white __pd mnh80">
        <div class="_fw">
          <div class="flex items-center">
            <div class="font-1r tw-six">Coverage Admin</div>
            <q-btn flat icon="mdi-plus" color="primary" @click="dialog = true"></q-btn>
          </div>
          <div class="_fw q-py-md">
            <div class="w500 mw100">
              <q-input placeholder="Search options..." v-model="search.text">
                <template v-slot:prepend>
                  <q-icon name="mdi-magnify"></q-icon>
                </template>
              </q-input>
            </div>
            <div class="row items-center">
            <div class="flex items-start q-py-md">
              <div class="col-shrink">
                <q-icon size="25px" color="primary" name="mdi-filter" class="q-my-sm"></q-icon>
              </div>
              <div class="col-grow">
                <type-picker removable @remove="type = undefined" v-model="type"></type-picker>
              </div>
            </div>
              <q-checkbox size="sm" v-model="pub" label="Public Templates"></q-checkbox>
            </div>
          </div>

          <q-scroll-area id="I_S" @scroll="senseScrollLoad">
            <div class="_fw __cg">
              <div v-for="(cov, i) in h$.data || []" :key="`cov-${i}`"
                   class="__i_s _fw br10 bs2-5 q-pa-lg bg-white relative-position" :id="`i_s-${i}`">
                <q-btn class="t-r-a" flat dense icon="mdi-dots-horizontal">
                  <q-menu>
                    <div class="q-pa-md w300 br10 bg-white">
                      <q-list separator>
                        <q-item :clickable="!copying" @click="copyCoverage(cov)">
                          <q-item-section>
                            <q-item-label>Make Copy</q-item-label>
                          </q-item-section>
                          <q-item-section side>
                            <q-spinner v-if="copying"></q-spinner>
                            <q-icon v-else name="mdi-content-copy"></q-icon>
                          </q-item-section>
                        </q-item>
                        <q-item clickable @click="openCoverage(cov)">
                          <q-item-section>
                            <q-item-label>View</q-item-label>
                          </q-item-section>
                          <q-item-section side>
                            <q-icon name="mdi-eye"></q-icon>
                          </q-item-section>
                        </q-item>
                        <q-item clickable @click="editing = cov">
                          <q-item-section>
                            <q-item-label>Edit</q-item-label>
                          </q-item-section>
                          <q-item-section side>
                            <q-icon name="mdi-pencil"></q-icon>
                          </q-item-section>
                        </q-item>
                      </q-list>
                    </div>
                  </q-menu>
                </q-btn>
                <coverage-card :model-value="cov"></coverage-card>
              </div>
              <div class="q-pa-lg">
                <q-spinner size="50px" color="primary" v-if="h$.isPending"></q-spinner>
              </div>
            </div>
          </q-scroll-area>
          <div class="row q-py-md">
            <div class="font-7-8r">{{h$.data?.length ? 1 : 0}} - {{(h$.data?.length || 0)}} of {{h$.total}}</div>
          </div>
          <!--      <div class="row _fw q-pa-sm">-->
          <!--        <q-space></q-space>-->
          <!--        <q-pagination-->
          <!--            color="black"-->
          <!--            :max="pagination.pageCount"-->
          <!--            :min="1"-->
          <!--            :model-value="pagination.currentPage"-->
          <!--            @update:model-value="h$.toPage($event)"-->
          <!--            direction-links-->
          <!--            boundary-numbers-->
          <!--        ></q-pagination>-->
          <!--      </div>-->

        </div>


        <common-dialog setting="right" @update:model-value="closeDialog" :model-value="dialog || !!editing">
          <div class="_fw q-pa-md _oxh">
            <coverage-form console :added-by="org" :template="!org" :model-value="editing"></coverage-form>
          </div>
        </common-dialog>
      </div>
    </div>

  </q-page>
</template>

<script setup>
  import TypePicker from 'components/coverages/cards/TypePicker.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import CoverageForm from 'components/coverages/forms/CoverageForm.vue';
  import CoverageCard from 'components/coverages/cards/CoverageCard.vue';

  import {HFind, hInfiniteScroll} from 'src/utils/hFind';
  import {computed, ref} from 'vue';
  import {HQuery} from 'src/utils/hQuery';
  import {useCoverages} from 'stores/coverages';
  import {useRouter} from 'vue-router';
  import {_pick} from 'symbol-syntax-utils';
  import {$errNotify} from 'src/utils/global-methods';
  import { pickCoverageFieldsForCopy } from '../utils/copy';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const router = useRouter();
  const store = useCoverages();
  const orgStore = useOrgs();

  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const props = defineProps({
    org: { required: false }
  })

  const { item:fullOrg } = idGet({
    store: orgStore,
    value: computed(() => props.org || getOrgId.value)
  })

  const type = ref(undefined);

  const editing = ref(undefined);
  const dialog = ref(false);
  const closeDialog = (val) => {
    if (!val) {
      dialog.value = false;
      editing.value = undefined;
    }
  }
  const { search, searchQ } = HQuery({})

  const pub = ref(false);
  const params = computed(() => {
    const q = {};
    if (type.value) q.type = type.value;
    if (fullOrg.value._id) {
      const id = fullOrg.value._id;
      q.$or = [{ issuer: id }, { org: id }]
    } else if(pub.value) q.public = true;
    return {
      query: {
        ...q,
        ...searchQ.value
      }
    }
  })
  const { h$, pagination } = HFind({
    store,
    limit: ref(10),
    params
  })

  const { senseScrollLoad } = hInfiniteScroll({ h$, pagination, loadNum: 10 })

  const openCoverage = (cov) => {
    const { href } = router.resolve({ name: 'coverage', params: { id: cov._id } });
    window.open(href, '_blank');
  }

  const copying = ref(false);
  const copyCoverage = async (val) => {
    if (!copying.value) {
      copying.value = true;
      const rest = _pick(val, pickCoverageFieldsForCopy);
      if(val.template) rest.fromTemplate = val._id
      rest.name = `Copy of ${rest.name}`
      await store.create(rest)
          .catch(err => $errNotify(`Error copying template: ${err.message}`));
      copying.value = false;
    }
  }

</script>

<style lang="scss" scoped>
  .__cg {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 550px));
    grid-template-rows: repeat(auto-fit, auto);
    grid-gap: 10px;
    justify-content: center;
    overflow-y: scroll;
  }

  .__cp {
    min-height: 100vh;
    padding: 3vw 0;
  }

  #I_S {
    height: 800px;
  }

  .__pd {
    padding: 4vh 2vw;
  }
</style>
