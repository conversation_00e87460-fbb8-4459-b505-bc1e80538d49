<template>
  <div class="_fw">


    <q-expansion-item group="20" expand-icon="mdi-menu-down">
      <template v-slot:header>
        <q-item class="_fw">
          <q-item-section>
            <q-item-label class="tw-six">Statewide Rates</q-item-label>
          </q-item-section>
        </q-item>
      </template>
      <div class="__b">
        <div class="q-pa-sm tw-six font-7-8r text-p6">Statewide Rates</div>
        <premium-form v-model="form.premium" @update:model-value="autoSave('premium')"></premium-form>
      </div>
    </q-expansion-item>

    <q-separator></q-separator>

    <q-expansion-item group="20" expand-icon="mdi-menu-down">
      <template v-slot:header>
        <q-item class="_fw">
          <q-item-section>
            <q-item-label class="tw-six">Local Areas</q-item-label>
          </q-item-section>
        </q-item>
      </template>

      <div class="__b">
        <div class="q-pa-sm tw-six font-7-8r text-p6">Local Rates</div>

        <!--        AREA LIST-->
        <q-list separator dense>
          <add-item @click="addArea" :label="`New ${form.state} Area`"></add-item>
          <q-item v-for="(area, i) in form?.areas || []" :key="`area-${i}`" clickable @click="activeArea = i">
            <q-item-section>
              <q-item-label>{{ area.name || $possiblyPlural('Zip', area.zips) }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>

        <q-slide-transition>
          <div class="_fw" v-if="activeArea > -1">
            <div class="_form_grid">
              <div class="_form_label">Zip Codes</div>
              <div class="q-pa-sm">
                <div class="flex items-center" v-if="!!form?.state">
                  <q-radio label="By County" v-model="searchType" val="county"></q-radio>
                  <q-radio label="By City" v-model="searchType" val="city"></q-radio>
                  <q-radio label="By Zip" v-model="searchType" val="zip"></q-radio>
                </div>

                <div class="flex items-center">
                  <zip-picker
                      v-if="searchType === 'zip'"
                      color="transparent"
                      :state="form?.state"
                      @update:model-value="setZips"
                  ></zip-picker>
                  <county-picker
                      color="transparent"
                      v-if="searchType === 'county'"
                      :state="form?.state"
                      @update:zips="setZips"
                  ></county-picker>
                  <city-picker
                      color="transparent"
                      v-if="searchType === 'city'"
                      :state="form?.state"
                      @zip-data="setCity"
                  ></city-picker>
                  <q-chip v-if="activeArea > -1 && form.areas[activeArea]?.zips" color="transparent" clickable @click="form.areas[activeArea].zips = []">
                    <span class="q-mr-sm">Clear All</span>
                    <q-icon name="mdi-close" color="red"></q-icon>
                  </q-chip>
                </div>

                <div class="q-py-sm">
                  <q-chip
                      color="white"
                      v-for="(z, i) in form.areas[activeArea]?.zips || []"
                      :key="`zips-${i}`"
                      size="sm"
                      removable
                      icon-remove="mdi-cancel"
                      @remove="removeZip(i)"
                      :label="z"></q-chip>
                </div>
              </div>
            </div>
            <premium-form v-model="form.areas[activeArea].premium" @update:model-value="updateArea"></premium-form>
          </div>
        </q-slide-transition>
      </div>
    </q-expansion-item>

    <common-dialog v-model="confirmDialog" setting="small">
      <div class="q-pa-md bg-white">
        <div class="font-1r tw-six">The following zip codes already have rate areas - do you want to reassign them?
        </div>
        <div class="q-py-sm">
          <span v-for="(o, i) in overlaps" :key="`ovr-${i}`">{{ o.zip }}{{ i < overlaps.length - 1 ? ', ' : '' }}</span>
        </div>

        <div class="q-pt-sm row justify-end">
          <q-btn no-caps size="sm" @click="cancelOverrids">
            <span class="q-mr-sm">Cancel</span>
            <q-icon name="mdi-close" color="red"></q-icon>
          </q-btn>

          <q-btn class="q-ml-md" size="sm" flat @click="setZips(undefined, true)">
            <span class="q-mr-sm">Reassign</span>
            <q-icon name="mdi-check" color="green"></q-icon>
          </q-btn>
        </div>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import ZipPicker from 'components/common/geo/pickers/ZipPicker.vue';
  import CountyPicker from 'components/common/geo/pickers/CountyPicker.vue';
  import CityPicker from 'components/common/geo/pickers/CityPicker.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import PremiumForm from 'components/coverages/forms/PremiumForm.vue';
  import AddItem from 'components/common/buttons/AddItem.vue';

  import {computed, ref, watch} from 'vue';

  import {$possiblyPlural} from 'src/utils/global-methods';
  import {HForm, HSave} from 'src/utils/hForm';
  import {useRates} from 'stores/rates';
  import {idGet} from 'src/utils/id-get';

  const rateStore = useRates();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: false },
    coverage: { required: true }
  })

  const { item: rate } = idGet({
    store: rateStore,
    value: computed(() => props.modelValue)
  })

  const state = ref('');

  const searchType = ref('county');

  const formFn = (defs) => {
    return {
      state: '',
      ...defs
    }
  }

  const { form } = HForm({
    store: rateStore,
    value: rate,
    beforeFn: (val) => {
      if (!val.coverage) val.coverage = props.coverage?._id || props.coverage;
      return val;
    },
    log: false,
    afterFn: (val) => {
      emit('update:model-value', val);
    },
    formFn
  })

  const { autoSave } = HSave({ form, store: rateStore })

  const activeArea = ref(-1);
  const areaDef = (defs) => {
    return {
      name: '',
      zips: [],
      ...defs
    }
  }
  const addArea = () => {
    if (form.value.areas) form.value.areas.unshift(areaDef());
    else form.value.areas = [areaDef()];
    activeArea.value = 0;
  }

  const confirmDialog = ref(false);
  const overlaps = ref([]);
  const zips = ref([]);

  const cancelOverrids = () => {
    confirmDialog.value = false;
    zips.value = [];
    overlaps.value = [];
  }

  const setZips = (val, override) => {
    if (val) zips.value = Array.isArray(val) ? val : [val];
    //check if zip is already used
    if (!override) {
      form.value.areas.forEach((area, i) => {
        for (const z of zips.value) {
          const idx = (area.zips || []).indexOf(z);
          if (idx > -1) {
            overlaps.value.push({ areaIdx: i, zipIdx: idx })
          }
        }
      })
    } else {
      for (const ex of overlaps) {
        form.value.areas[ex.areaIdx].zips.splice(ex.zipIdx, 1);
      }
    }
    if (!override && overlaps.value?.length) confirmDialog.value = true;
    else {
      const area = form.value.areas[activeArea.value] || {};
      const zps = Array.from(new Set([...area.zips, ...zips.value]));
      form.value.areas[activeArea.value].zips = zps
      autoSave('areas');
      cancelOverrids();
    }
  }

  const setCity = (val) => {
    setZips(val.zips)
  }

  const updateArea = () => {
    activeArea.value = -1;
    autoSave('areas')
  }

  const removeZip = (i) => {
    form.value.areas[activeArea.value].zips.splice(i, 1);
  }

  watch(() => props.modelValue, (nv) => {
    if (nv) form.value = Object.assign({}, nv);
  })
</script>

<style lang="scss" scoped>
  .__b {
    padding-left: 15px;
    border-left: solid 10px var(--q-p0);
  }
</style>
