<template>
  <div class="_fw">
    <div class="q-pa-sm">
      <div class="font-7-8r">Plan Year:&nbsp; <span class="tw-six text-p6 font-1-1-4r">{{ fromTo }}</span></div>
    </div>
    <action-row :model-value="enrollment"></action-row>

    <q-separator class="q-my-md"></q-separator>

    <div class="_fw q-pa-sm">

      <member-row append="Enrolled" :members="enrollees" plural=""></member-row>

      <div class="q-py-sm"></div>
      <care-wallet :enrollment="enrollment"></care-wallet>

    </div>
  </div>
</template>

<script setup>
  import ActionRow from 'components/enrollments/cards/ActionRow.vue';
  import CareWallet from 'components/enrollments/cafe/CareWallet.vue';
  import MemberRow from 'components/enrollments/cards/MemberRow.vue';

  import {computed} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {customValues} from 'components/plans/utils';
  import {getCurrentPlanYear} from 'components/plans/utils';
  import {useEnvStore} from 'stores/env';

  const envStore = useEnvStore();
  const planStore = usePlans();

  const props = defineProps({
    modelValue: { required: true }
  })

  const enrollment = computed(() => props.modelValue);

  const { item: fullPlan } = idGet({
    value: computed(() => enrollment.value?.plan || envStore.getPlanId),
    store: planStore
  })

  const fromTo = computed(() => {
    if (fullPlan.value) {
      const from = customValues.value.planYearStart.value(fullPlan.value, undefined, 'MMM D')
      const to = customValues.value.planYearEnd.value(fullPlan.value, undefined, 'MMM D')
      const yr = enrollment.value?.planYear || getCurrentPlanYear(fullPlan.value);
      return `${from}, ${yr} - ${to}, ${Number(yr) + 1}`
    } else return 'Plan loading...'
  })

  const enrollees = computed(() => {
    const arr = [];
    for(const k in enrollment.value?.enrolled){
      arr.push(enrollment.value.enrolled[k]);
    }
    return arr;
  })
</script>

<style lang="scss" scoped>

</style>
