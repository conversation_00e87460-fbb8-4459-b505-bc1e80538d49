<template>
  <div class="_fw _bg_ow">
    <div class="row justify-center">
      <div class="_cent">
        <div class="tw-six font-1r q-py-md">Coverages By Type</div>
        <div class="row items-center q-pb-md">
          <template v-for="(t, i) in enrolledTypes" :key="`type-${i}`">
            <q-chip clickable @click="tab = t" :class="tab === t ? 'tw-six' : ''" dense square color="transparent">
              <q-avatar size="10px" :color="typeColors[t]"></q-avatar>
              <span class="q-ml-sm">{{ types[t] }} ({{byType[t].total || 0}})</span>
            </q-chip>
            <div class="text-grey-7" v-if="i < enrolledTypes.length - 1">|</div>
          </template>
        </div>

        <div class="row" v-if="!!tab">
          <div class="col-12 col-md-6 q-pa-sm" v-for="(cov, i) in byType[tab]?.data || []" :key="`cov-${i}`">
            <div class="__c">
              <enrolled-coverage-card :model-value="cov" :enrollment="current"></enrolled-coverage-card>
            </div>
          </div>
        </div>
        <div class="q-px-lg" v-else>
          <i>Select Coverage Type</i>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup>
  import EnrolledCoverageCard from 'components/coverages/cards/EnrolledCoverageCard.vue';

  import {HFind} from 'src/utils/hFind';
  import {computed, ref, watch} from 'vue';
  import {loginPerson} from 'stores/utils/login';
  const { person } = loginPerson()
  import {useEnrollments} from 'stores/enrollments';
  import {useCoverages} from 'stores/coverages';
  import {typeColors, types} from 'components/coverages/utils/types';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {useEnvStore} from 'stores/env';

  const enrollmentsStore = useEnrollments();
  const cStore = useCoverages();
  const planStore = usePlans();
  const envStore = useEnvStore();

  const props = defineProps({
    plan: { required: false },
    enrollment: { required: false }
  })

  const tab = ref('dc');
  const year = ref(new Date().getFullYear().toString());

  const { item:fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan || envStore.getPlanId)
  });

  const ePause = computed(() => !person.value);
  const { h$: e$ } = HFind({
    store: enrollmentsStore,
    pause: ePause,
    limit: ref(2),
    params: computed(() => {
      const q = {
        $sort: { version: -1 },
        plan: fullPlan.value?._id,
        planYear: year.value,
        person: person.value?._id
      };
      return {
        query: q
      }
    })
  })

  const current = computed(() => props.enrollment || e$.data ? e$.data[0] : undefined);

  const { h$:c$ } = HFind({
    store: cStore,
    pause: computed(() => !current.value?.coverages),
    limit: computed(() => Object.keys(current.value?.coverages || {}).length),
    params: computed(() => {
      return {
        query: {
          _id: { $in: Object.keys(current.value?.coverages || {}) }
        }
      }
    })
  })

  const enrolledTypes = computed(() => {
    return Array.from(new Set((c$.data || []).map(a => a.type)))
  })

  watch(enrolledTypes, (nv, ov) => {
    if(nv?.length === 1 && ov?.length !== 1){
      tab.value = nv[0];
    } else if(nv && !nv.includes(tab.value)) tab.value = nv.includes('dc') ? 'dc' : nv[0]
  }, { immediate: true })

  const byType = computed(() => {
    const obj = {};
    for (const k in types.value) {
      const c = (c$.data || []).filter(a => a.type === k)
      obj[k] = {
        total: c.length,
        data: c
      }
    }
    return obj;
  })

  const tabs = computed(() => {
    return {
      'Medical Coverage': {},
      'ICHRA': {},
    }
  })
</script>

<style lang="scss" scoped>
  .__c {
    border-radius: 10px;
    box-shadow: 0 3px 10px -3px #999;
    background: white;
    padding: 30px 2vw;
  }
</style>
