<template>
  <div class="_fw">
    <div class="row items-center q-py-md">
      <q-btn v-if="addDialog" flat text-color="secondary" icon="mdi-chevron-left" @click="addDialog = false"></q-btn>

      <div class="font-1r tw-six">Recruits</div>
      <q-btn v-if="!addDialog" flat text-color="primary" icon="mdi-plus" @click="addDialog = true"></q-btn>
    </div>

    <q-chip color="ir-bg2" clickable>
      <span class="q-mr-sm">{{comp?.name || 'Select Package'}}</span>
      <q-icon name="mdi-menu-down" v-if="!comp"></q-icon>
      <q-btn v-else dense flat icon="mdi-close" color="red" @click="comp = undefined"></q-btn>
      <q-popup-proxy v-model="compMenu">
        <div class="w400 mw100 q-pa-sm">
          <q-input dense filled v-model="compSearch.text">
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
          <q-list separator>
            <q-item v-for="(c, i) in comps$.data" :key="`comp-${i}`" clickable @click="comp = c">
              <q-item-section>
                <q-item-label>{{c.name}}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <pay-chip :model-value="c"></pay-chip>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </q-popup-proxy>
    </q-chip>

    <q-tab-panels class="_panel" animated :model-value="!!addDialog">
      <q-tab-panel class="_panel" :name="true">
        <div class="w700 mw100">
          <template v-if="comp?._id">
            <comp-prospects :model-value="comp"></comp-prospects>
          </template>
          <div v-else class="q-pa-md text-italic font-1r">Select Compensation Package</div>
        </div>
      </q-tab-panel>
      <q-tab-panel class="_panel" :name="false">
        <div class="q-py-sm w600 mw100">
          <q-input filled v-model="search.text" dense>
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
        </div>

        <div class="__table">
          <table>
            <thead>
            <tr>
              <th v-for="(h, i) in cols" :key="`colh-${i}`">{{ h.label }}</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(c, i) in c$.data" :key="`cam-${i}`">
              <td v-for="(d, idx) in cols" :key="`col-${i}-${idx}`">
                <component :is="d.component" v-bind="d.attrs(c)"></component>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </q-tab-panel>
    </q-tab-panels>



  </div>
</template>

<script setup>
  import TdText from 'components/common/tables/TdText.vue';
  import StageChip from 'components/comps/utils/StageChip.vue';
  import CompProspects from 'components/comps/forms/CompProspects.vue';

  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {useCams} from 'stores/cams';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';
  import PayChip from 'components/comps/cards/PayChip.vue';
  import {useComps} from 'stores/comps';

  const orgStore = useOrgs()
  const camStore = useCams()
  const compStore = useComps()

  const props = defineProps({
    orG: { required: true },
    compId: { required: false }
  })

  const addDialog = ref(false);

  const { item: org } = idGet({
    value: computed(() => props.orG),
    store: orgStore
  })

  const comp = ref(undefined)
  const compMenu = ref(false);
  const { search:compSearch, searchQ:compSearchQ } = HQuery({})
  const { h$:comps$ } = HFind({
    store: compStore,
    pause: computed(() => !compMenu.value),
    params: computed(() => {
      return {
        query: {
          org: org.value._id,
          ...compSearchQ.value
        }
      }
    })
  })

  const stages = ref([]);

  const { search, searchQ } = HQuery({})
  const { h$: c$ } = HFind({
    store: camStore,
    params: computed(() => {
      const query = {
        ...searchQ.value,
        active: false,
        org: org.value._id
      }
      if (props.compId) query.comp = props.compId;
      if (stages.value.length) query.stage = { $in: stages.value }
      return {
        runJoin: {
          cams_person: true,
          cams_comp: true
        },
        query
      }
    })
  })

  const cols = computed(() => [
    {
      label: 'Prospect',
      component: TdText,
      attrs: (c) => {
        return { col: { value: c._fastjoin?.person?.name } }
      }
    },
    {
      label: 'Package',
      component: TdText,
      attrs: (c) => {
        return { col: { value: c._fastjoin?.comp?.name } }
      }
    },
    {
      label: 'Stage',
      component: StageChip,
      attrs: (c) => {
        return { modelValue: c.stage }
      }
    }
  ])

</script>

<style lang="scss" scoped>

  .__table {
    width: 100%;
    overflow-x: scroll;

    table {
      width: 100%;
      border-collapse: collapse;

      tr {
        th {
          padding: 4px 8px;
          font-weight: 600;
          color: var(--ir-mid);
          text-align: left;
          font-size: .8rem;
        }

        td {
          padding: 4px 8px;
        }

        &:nth-child(even) {
          background: var(--ir-bg1);
        }
      }
    }
  }
</style>
