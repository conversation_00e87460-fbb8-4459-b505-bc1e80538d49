<template>
  <div class="_fw">
    <q-tab-panels v-model="tab" animated class="_panel">
      <q-tab-panel class="_panel" name="assign">

        <q-select v-if="!comp" placeholder="Search comp packages..." v-model="comp" :options="h$.data || []" use-input
                  @input-value="search.text = $event">
          <template v-slot:option="scope">
            <comp-item :model-value="scope.opt" clickable @click="comp = scope.opt">
            </comp-item>
          </template>
        </q-select>
        <q-slide-transition>
          <div v-if="comp" class="_fw">
            <div class="_fw q-pa-md">
            <comp-form
                :model-value="noId(comp)"
                :personId="personId"
                :org="orgId"
                service="cams"
                @update:model-value="comp = $event"
                :comp-id="comp._id"
                removable
            ></comp-form>
            </div>
          </div>
        </q-slide-transition>
      </q-tab-panel>
      <q-tab-panel class="_panel relative-position" name="card">
        <q-btn dense flat icon="mdi-dots-vertical" @click="editing = true" class="t-r-a"></q-btn>
        <comp-card v-if="!editing" editing service="cams" :model-value="cam"></comp-card>
        <q-slide-transition>
          <div class="_fw" v-if="editing">
            <div class="row">
              <q-btn dense flat icon="mdi-close" color="red" @click="editing = false"></q-btn>
            </div>
            <comp-form
                removable
                :model-value="cam"
                :org="orgId"
                service="cams"
                @update:model-value="comp = $event, editing = false"
            ></comp-form>
          </div>
        </q-slide-transition>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup>
  import CompCard from 'src/components/comps/cards/CompCard.vue';
  import CompItem from 'src/components/comps/cards/CompItem.vue';
  import CompForm from 'src/components/comps/forms/CompForm.vue';

  import {idGet} from 'src/utils/id-get';
  import {useCams} from 'src/stores/cams';
  import {computed, ref, watch} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useComps} from 'stores/comps';
  import {HQuery} from 'src/utils/hQuery';
  import { pickCamsFieldsForCopy } from 'components/comps/cams/utils';
  import {_pick} from 'symbol-syntax-utils';

  const store = useCams();

  const props = defineProps({
    modelValue: { required: true },
    orgId: { required: true },
    personId: { required: true },
  })
  const { item: cam } = idGet({
    store,
    value: props.modelValue
  })

  const comp = ref(undefined);
  const tab = ref('assign');

  const editing = ref(false);

  const noId = (c) => {
    if(!c) return undefined;
    else return _pick(c, pickCamsFieldsForCopy);
  }

  const { search, searchQ } = HQuery({})

  const { h$ } = HFind({
    store: useComps(),
    params: computed(() => {
      return {
        query: { org: props.orgId, ...searchQ.value }
      }
    })
  })

  watch(cam, (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      tab.value = 'card';
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>

</style>
