<template>
  <q-page>
    <div class="row justify-center">
      <div class="_cent pd4 pw2 mnh60">
        <div class="q-pa-md">
          <div class="tw-six font-1-1-4r">Permissions Sets</div>
          <div class="font-1r">Create groups of people and grant them in-app permissions</div>
        </div>
        <div class="row q-pb-md">
          <q-btn @click="dialog = !dialog" no-caps class="_inp" color="white">
            <span class="q-mr-sm text-black">Add New</span>
            <q-icon name="mdi-plus" color="primary"></q-icon>
          </q-btn>
        </div>
        <div class="row">
          <div class="col-12 col-md-4 col-lg-3">
            <div class="__c cursor-pointer" v-for="(c, i) in Object.keys(cap?.caps || {})" :key="`cap-${i}`" @click="editing = c">
              <org-cap-card :model-value="capRecord.caps[c]" :name-key="c"></org-cap-card>
            </div>
          </div>
        </div>
      </div>
    </div>

    <common-dialog setting="right" @update:model-value="toggleDialog" :model-value="dialog || !!editing">
      <div class="_fw q-pa-md bg-white">
        <org-cap-form :need="defNeed" :caps="caps" :model-value="cap" :path="editing" @update:path="editing = $event"></org-cap-form>
      </div>
    </common-dialog>
  </q-page>
</template>

<script setup>
  import OrgCapCard from 'components/capabilities/orgs/cards/OrgCapCard.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import OrgCapForm from 'components/capabilities/orgs/forms/OrgCapForm.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {useOrgs} from 'stores/orgs';
  import {useCaps} from 'stores/caps';
  import {defaultHierPart, defaultScheme} from 'src/utils/ucans';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const orgStore = useOrgs();
  const capStore = useCaps()

  const { item: org } = idGet({
    store: orgStore,
    value: getOrgId
  })

  const dialog = ref(false);
  const editing = ref('');
  const toggleDialog = (val) => {
    if(!val) {
      dialog.value = false;
      editing.value = '';
    }
  }

  const capRecord = ref({})
  const { item:cap } = idGet({
    store: capStore,
    value: capRecord
  })
  watch(org, async (nv) => {
    if(nv?._id && capRecord.value?.subject !== nv._id){
      const data = await capStore.find({ query: { subject: nv._id, $limit: 1 }})
      if(data.total) capRecord.value = data.data[0];
      else {
        capRecord.value = await capStore.create({ subject: nv._id })
      }
    }
  }, { immediate: true });

  const wit = { scheme: defaultScheme, hierPart: defaultHierPart };

  const defNeed = computed(() => [['orgs', 'WRITE'], [`orgs:${cap.value.subject}`, '*']])
  const caps = computed(() => {
    return {
      '*': {
        label: 'Admin',
        description: 'Members will have all abilities for this organization',
        need: [['orgs', 'WRITE'], [`orgs:${cap.value.subject}`, '*']],
        cap: {
          with: wit,
          can: { namespace: `orgs:${cap.value.subject}`, segments: ['*'] }
        }
      },
      orgAdmin: {
        label: 'Organization Admin',
        description: 'Members will have ability to edit organization details, add/edit members, add/edit plans',
        need: [['orgs', 'WRITE'], [`orgs:${cap.value.subject}`, 'orgAdmin']],
        cap: {
          with: wit,
          can: { namespace: `orgs:${cap.value.subject}`, segments: ['orgAdmin'] }
        }
      }
    }
  })

</script>

<style lang="scss" scoped>
  .__c {
    padding: 20px 2vw;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px -6px #999;
  }
</style>
