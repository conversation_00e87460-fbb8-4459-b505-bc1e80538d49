<template>
  <div :id="id" class="_fw q-px-md">

    <template v-if="!saved">

      <div class="row q-py-md">

        <slot name="top" :form="form" :modelForm="modelForm" :setField="setField" :getField="getField"
              :fieldAttrs="fieldAttrs"></slot>

        <q-input
            v-bind="{
            dark: dark,
            filled: true,
            placeholder: 'Your Name',
            inputClass: 'q-px-sm',
            class: '_fw',
                ...fieldAttrs,
                ...nameFieldAttrs
              }"
            v-model="form.name"
        >

        </q-input>

      </div>
      <div class="row q-py-md">
        <q-input
            v-bind="{
            dark: dark,
            filled: true,
            placeholder: 'Your Email',
            inputClass: 'q-px-sm',
            class: '_fw',
                ...fieldAttrs,
                ...emailFieldAttrs
              }"
            v-model="form.email"

        >

        </q-input>
      </div>
      <div class="row q-py-md">
        <phone-input
            v-model="form.phone"
            :input-attrs="{
            filled: true,
            dark: dark,
            placeholder: 'Phone Number',
            inputClass: 'q-px-sm',
            class: '_fw',
                ...fieldAttrs,
                ...phoneFieldAttrs
              }"
        >
        </phone-input>
      </div>

      <slot name="beforeBody" :form="form" :modelForm="modelForm" :setField="setField" :getField="getField"></slot>

      <div class="row q-py-md">

        <q-input
            v-model="form.body"
            v-bind="{
            filled: true,
            dark: dark,
            type: 'textarea',
            placeholder: 'Pour your heart out',
            inputClass: 'q-px-sm',
            class: '_fw',
                ...fieldAttrs,
                ...bodyFieldAttrs
              }"
        >

        </q-input>

      </div>

      <slot name="bottom" :form="form" :modelForm="modelForm" :setField="setField" :getField="getField"></slot>

      <div class="row justify-end q-pt-md">
        <div class="col-2"></div>
        <q-btn
            v-bind="{
                  outline: true,
                  color: accentColor,
                  label: 'Send',
                  icon: 'mdi-send',
                  ...btnAttrs
                }"
            @click="save()"
        ></q-btn>
      </div>
    </template>
    <q-slide-transition>
      <template v-if="saved">
        <div class="_fw flex flex-center" style="min-height: 400px">
          <div class="text-md text-weight-bold q-pa-lg text-center">{{ afterMessage }}</div>
        </div>
      </template>
    </q-slide-transition>
  </div>
</template>

<script setup>
  import {useReqs} from 'src/stores/reqs';
  import {HForm} from 'src/utils/hForm';
  import {computed, onMounted, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import PhoneInput from 'src/components/common/phone/PhoneInput.vue';
  import {_get, _set} from 'symbol-syntax-utils';

  const reqStore = useReqs();

  const props = defineProps({
    dark: Boolean,
    toggle: { type: Boolean, default: true },
    title: String,
    caption: String,
    btnAttrs: Object,
    textColor: { type: String, default: 'primary' },
    accentColor: { type: String, default: 'accent' },
    bg: String,
    start: String,
    fieldAttrs: Object,
    nameFieldAttrs: Object,
    emailFieldAttrs: Object,
    phoneFieldAttrs: Object,
    bodyFieldAttrs: Object,
    defs: Object,
    id: { default: 'ContactBase' },
    afterMessage: { type: String, default: 'Thanks! We\'ll reach out asap.' },
    validate: { type: Boolean, default: true },
    vOpts: Object
  });

  const tab = ref('us');

  const saved = ref(false);

  const formDefaults = computed(() => {
    const fp = LocalStorage.getItem('fpId');
    const refName = LocalStorage.getItem('refName');
    return { fingerprint: fp, refName, ...props.defs };
  });

  const useVOpts = computed(() => {
    if (props.vOpts) return props.vOpts;
    else {
      let obj = {
        email: { name: 'Email', v: ['email'] }
      };
      if (!form.value.email) {
        obj = {
          phone: {
            name: 'Phone', v: {
              check: 'phone',
              err: 'Phone or email required'
            }
          }
        }
      }
      return obj
    }
  });

  const { form, save } = HForm({
    store: reqStore,
    log: false,
    validate: props.validate,
    vOpts: useVOpts,
    formDefaults,
    afterFn: () => {
      saved.value = true;
      form.value = Object.assign({}, formDefaults.value)
    },
    successMessage: 'We will be in touch!'
  });


  const modelForm = (field, val) => {
    if (typeof field === 'string') form.value[field] = val
    else form.value = _set(form.value, [...field], val);
  };

  const getField = (key, property, opts = { path: 'fields' }) => {
    const { path } = opts;
    let val = _get(form.value, [path], []).find(a => a.key === key);
    if (property) return _get(val, property);
    else return val;
  };

  const setField = (value, property, opts = { path: 'fields', key: 'key' }) => {
    const { path, key, label } = opts;
    const idx = _get(form.value, [path], []).map(a => a[key]).indexOf(property);
    const l = label ? label : _get(form.value, [path, idx, 'label'], property);
    const pl = { value, [key]: property, label: l };
    if (idx > -1) {
      form.value[path].splice(idx, 1, pl);
    } else if (form.value[path]) {
      form.value[path].push(pl);
    } else form.value = _set(form.value, path, [pl]);
  };

  onMounted(() => {
    if (props.start) tab.value = props.start;
  });

</script>

<style scoped>

</style>
