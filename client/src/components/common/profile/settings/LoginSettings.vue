<template>
  <div>
    <div class="row">
      <div class="col-12 col-md-4 q-pa-md">
        <div class="_fw q-py-sm" v-for="(type, idx) in Object.keys(items)" :key="`type-${idx}`">
          <q-btn
              :class="`_fw text-weight-bold text-left bg-${type === tab ? 'p0' : 'white'}`"
              flat
              no-caps
              text-color="ir-grey-8"
              @click="tab = type"
              rounded
          >
            <div class="_fw text-left">{{ items[type].label }}</div>
          </q-btn>
        </div>
      </div>
      <div class="col-12 col-md-8 q-pa-md">
        <component
            :is="items[tab].component"
            v-bind="items[tab].attrs || {}"
        ></component>
      </div>
    </div>
  </div>
</template>

<script setup>
  import PasswordManager from 'src/components/auth/traditional/PasswordManager.vue';
  import OauthList from 'src/components/auth/oauth/OauthList.vue';
  import LogoutCard from 'src/components/auth/utils/LogoutCard.vue';
  import DeleteAccount from 'src/components/auth/utils/DeleteAccount.vue';
  import WebAuthnManager from 'components/auth/webauthn/components/WebAuthnManager.vue';

  import {computed, ref} from 'vue';

  const tab = ref('device');
  const items = computed(() => {
    return {
      'device': {
        label: 'Device Security',
        component: WebAuthnManager,
        attrs: {}
      },
      'social': {
        label: 'Social Accounts',
        component: OauthList,
        attrs: {}
      },
      'password': {
        label: 'Password',
        component: PasswordManager,
        attrs: {}
      },
      'logout': {
        label: 'Logout',
        component: LogoutCard,
        attrs: {
          div: {
            class: 'fw q-pa-lg'
          },
          button: {
            label: 'Logout of Account',
            flat: true,
            iconRight: 'mdi-chevron-right',
            noCaps: true,
            class: 'text-weight-bold'
          }
        }
      },
      'delete': {
        label: 'Delete Account',
        component: DeleteAccount
      }
    };
  })
</script>

<style lang="scss" scoped>

</style>
