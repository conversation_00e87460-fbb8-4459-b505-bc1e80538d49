<template>
  <div v-bind="{ class: '_fw __gr', ...divAttrs }">
    <div class="row justify-center">
      <div class="_cent">
        <div class="flex items-center">
          <div class="tw-six font-1-1-8r">{{ fullPlan?.name || 'Select Plan' }}</div>
          <q-btn v-if="p$.total" dense flat icon="mdi-menu-down"></q-btn>
          <q-popup-proxy>
            <div class="w400 mw100 bg-white q-pa-sm">
              <q-input v-model="search.text" dense filled>
                <template v-slot:prepend>
                  <q-icon name="mdi-magnify"></q-icon>
                </template>
              </q-input>
              <q-list separator>
                <q-item v-for="(p, i) in p$.data" :key="`p-${i}`" clickable @click="selectPlan(p)">
                  <q-item-section avatar>
                    <default-avatar :store="orgStore" :model-value="p.org"></default-avatar>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{p.name}}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
          </q-popup-proxy>
        </div>
        <div class="alt-font font-3-4r tw-six text-uppercase">{{subTitle}}</div>

      </div>
    </div>
  </div>
</template>

<script setup>
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {usePlans} from 'stores/plans';
  import {HFind} from 'src/utils/hFind';
  import {useOrgs} from 'stores/orgs';
  import {useRouter} from 'vue-router';
  import {HQuery} from 'src/utils/hQuery';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const router = useRouter();

  const planStore = usePlans();
  const orgStore = useOrgs();
  const envStore = useEnvStore();

  const emit = defineEmits(['update:plan'])
  const props = defineProps({
    plan: { required: false },
    subTitle: { default: 'Care Navigation' },
    changeFn: Function,
    divAttrs: Object
  })

  const { getOrgId } = contextItems(envStore)

  const { item: fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan || envStore.getPlanId)
  })

  watch(fullPlan, (nv, ov) => {
    if(nv && nv._id !== ov?._id) {
      setTimeout(() => {
        envStore.setPlanId(nv._id);
      }, 1000)
    }
  }, { immediate: true })

  const { item:org } = idGet({
    store: orgStore,
    value: getOrgId
  })

  const selectPlan = (p) => {
    envStore.setPlanId(p._id)
    // if(p.org && p.org !== envStore.getOrgId) envStore.setOrgId(p.org);
    if(props.changeFn) props.changeFn(p)
    router.go(0)
  }

  const { h$:o$ } = HFind({
    store: orgStore,
    limit: ref(10),
    params: computed(() => {
      return {
        query: {
          _id: { $in: Object.keys(org.value?.control || {}).map(a => a.split('.')).flat(1) }
        }
      }
    })
  })

  const allGroups = computed(() => {
    let list = [];
    for(let i = 0; i < o$.data.length; i++){
      const arr = Object.keys(o$.data[i].groups || {});
      for(let o = 0; o < arr.length; o++){
        list.push(o$.data[i].groups[arr[o]]);
      }
    }
    return list
  })

  const pause = computed(() => {
    let all = true;
    const orgGroups = org.value?.groups || {}
    for(let i = 0; i < allGroups.value.length; i++){
      const id = allGroups.value[i];
      if(!orgGroups[id]) all = false;
    }
    return all;
  })

  const { search, searchQ } = HQuery({})
  const { h$:p$ } = HFind({
    store: planStore,
    pause,
    limit: ref(10),
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          $or: [
            { org: getOrgId.value },
            { groups: { $in: allGroups.value } }
          ]
        }
      }
    })
  })

  const firstId = computed(() => p$.data[0]?._id);

  watch(firstId, (nv, ov) => {
    if(nv && nv !== ov){
      setTimeout(() => {
        if(!envStore.getPlanId){
          envStore.setPlanId(nv)
        }
      }, 250)
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>
  .__gr {
    padding: 2vh 3vw;
    background: linear-gradient(12deg, var(--q-a1), var(--q-a0), var(--q-a2));
    color: var(--q-a12);
  }
</style>
