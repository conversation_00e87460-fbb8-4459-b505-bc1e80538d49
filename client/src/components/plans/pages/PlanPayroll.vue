<template>
  <div class="row justify-center">
    <div class="_cent">
      <plan-year-picker
          color="ir-bg2"
          prefix="Plan Year: "
          :model-value="planYear"
          @update:model-value="selectPlanYear($event)"
      ></plan-year-picker>
      <plan-payroll-report :plan-year="planYear" :plan="plan"></plan-payroll-report>
    </div>
  </div>
</template>

<script setup>
  import PlanPayrollReport from 'components/plans/cards/PlanPayrollReport.vue';
  import PlanYearPicker from 'components/plans/utils/PlanYearPicker.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, onMounted, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {usePlans} from 'stores/plans';
  import {getCurrentPlanYear} from 'components/plans/utils';
  import {useRoute, useRouter} from 'vue-router';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const envStore = useEnvStore();
  const { getPlanId } = contextItems(envStore);

  const router = useRouter();
  const route = useRoute();

  const planStore = usePlans();

  const { item: plan } = idGet({
    store: planStore,
    value: getPlanId
  })

  const planYearSelect = ref('');
  const planYear = computed(() => planYearSelect.value || getCurrentPlanYear(plan.value))

  const selectPlanYear = (val) => {
    planYearSelect.value = val;
    router.push({ ...route, query: { ...route.query, planYear: val}})
    window.history.pushState({}, '', href)
  }

  onMounted(() => {
    if(route.query.planYear) planYearSelect.value = route.query.planYear;
  })


</script>

<style lang="scss" scoped>

</style>
