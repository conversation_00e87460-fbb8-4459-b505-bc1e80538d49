<template>
  <div class="_fw">
    <div class="font-1r row items-center tw-six q-pa-md">
      <q-tabs no-caps indicator-color="primary" align="left" v-model="tab">
        <q-tab label="Your Team" name="team"></q-tab>
        <q-tab label="Offers" name="offers"></q-tab>
      </q-tabs>
      <q-space></q-space>
      <q-btn class="_i_i" dense flat icon="mdi-cog" color="primary" @click="adding = true"></q-btn>
    </div>

    <q-tab-panels class="_panel" v-model="tab">
      <q-tab-panel class="_panel" name="team">
        <div v-if="!Object.keys(plan.team || {}).length" class="q-pa-md text-italic">No team contracts accepted. Go to settings to open your plan for offers.</div>
        <div class="row q-py-md">
          <div class="col-12 col-md-4 q-pa-sm" v-for="(id, i) in Object.keys(plan?.team || {})" :key="`t-${i}`">
            <div class="__c">
              <team-member-card :model-value="plan.team[id]"></team-member-card>
            </div>
          </div>
        </div>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="offers">
        <plan-offers :plan="plan"></plan-offers>
      </q-tab-panel>
    </q-tab-panels>


    <common-dialog v-model="adding" setting="right">
      <div class="_fw q-pa-md bg-white">
        <team-member-form :plan="plan"></team-member-form>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import TeamMemberCard from 'components/plans/team/cards/TeamMemberCard.vue';
  import TeamMemberForm from 'components/plans/team/forms/TeamMemberForm.vue';
  import PlanOffers from 'components/plans/team/pages/PlanOffers.vue';

  import {computed, ref} from 'vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const planStore = usePlans();
  const envStore = useEnvStore();
  const { getPlanId } = contextItems(envStore);

  const { item: plan } = idGet({
    store: planStore,
    value: getPlanId
  })

  const adding = ref(false)
  const tab = ref('team');
</script>

<style lang="scss" scoped>
  .__c {
    border-radius: 15px;
    padding: 30px min(20px, 2vw);
    box-shadow: 2px 2px 8px -2px #dedede;
  }
</style>
