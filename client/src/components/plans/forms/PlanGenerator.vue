<template>
  <div class="_fw _bg_ow mnh90">
    <plan-select-top :change-fn="changeFn" :div-attrs="{ class: '__plan_top'}" subTitle="Plan Guide"
                     :plan="plan"></plan-select-top>
    <div class="row justify-center">
      <div class="_cent __cnt pw2 bg-white">

        <div class="row items-center q-py-md">
          <q-chip color="transparent" clickable>
            <span class="q-mr-sm">{{ tabs[route.meta?.name || route.name]?.label || 'Select Plan Section' }}</span>
            <q-icon name="mdi-menu-down" color="primary" size="18px"></q-icon>
            <q-popup-proxy>
              <div class="q-pa-sm w300 bg-white">
                <q-list>
                  <q-item v-show="!tabs[t].hide" clickable v-for="(t, i) in Object.keys(tabs)" :key="`t-${i}`"
                          @click="changeTab(t)">
                    <q-item-section>
                      <q-item-label>
                        {{ tabs[t].label }}
                      </q-item-label>
                    </q-item-section>
                    <q-item-section side>
                      <q-icon name="mdi-checkbox-marked" :color="tab === t ? 'green' : 'grey-2'"></q-icon>
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>
            </q-popup-proxy>
          </q-chip>
        </div>
        <template v-if="getPlanId">
          <router-view></router-view>
        </template>
        <template v-else>
          <div class="q-pa-xl text-italic font-1r">Select Plan</div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
  import PlanSelectTop from 'components/plans/utils/PlanSelectTop.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, watch} from 'vue';

  import {usePlans} from 'stores/plans';

  const store = usePlans()

  import {useRoute, useRouter} from 'vue-router';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const envStore = useEnvStore()

  const route = useRoute();
  const router = useRouter();

  const props = defineProps({
    modelValue: { required: false }
  })

  const { getPlanId } = contextItems(envStore)


  const { item: plan } = idGet({
    value: computed(() => props.modelValue || getPlanId.value),
    store
  })

  const tabs = computed(() => {
    return {
      'plan-care': { label: 'Care', name: 'plan-care' },
      'plan-enroll': { label: 'Enrollment', name: 'plan-gen-enroll' },
      'plan-funds': { label: 'Funds', name: 'plan-gen-funds' },
      'plan-payroll': { label: 'Payroll', name: 'plan-gen-payroll' },
      'plan-coverage': { label: 'Plan Coverage', name: 'plan-gen-coverage' },
      'plan-docs': { label: 'Docs', name: 'plan-gen-docs' },
      'plan-edit': { label: 'Settings', name: 'plan-gen-edit' },
      'plan-permissions': { label: 'Permissions', name: 'plan-gen-permissions' },
      'plan-team': { label: 'Team', name: 'plan-team' },
    }
  })

  const tab = computed(() => route.meta?.name || route.name);
  const changeTab = (t) => {
    router.push({ ...route, name: tabs.value[t].name })
    tab.value = t;
  }
  const changeFn = (p) => {
    router.push({ name: route.name, params: { planId: p._id } })
  }

  watch(() => route.query.planId, (nv) => {
    if (nv) {
      const current = envStore.getPlanId;
      if (nv !== current) {
        envStore.setPlanId(nv, 'plan-generator')
        router.go(0);
      }
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>
  .__ps {
    background: linear-gradient(3deg, var(--q-p1), white);
    border-radius: 12px;
  }

  .__plan_top {
    padding: 2vh 3vw;
    background: linear-gradient(180deg, var(--q-ir-grey-5), white);
    color: var(--q-p12);
  }

  .__cnt {
    padding-bottom: 2vh;
  }
</style>
