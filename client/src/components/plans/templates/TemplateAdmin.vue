<template>
  <q-page class="pa3 _bg_ow">
    <q-tab-panels animated class="_panel" :model-value="!!editing">
      <q-tab-panel class="_panel" :name="false">

        <div class="row q-py-md">
          <q-btn flat icon-right="mdi-plus" class="tw-six" no-caps label="Plan Template" @click="dialog = true"></q-btn>
        </div>

        <div class="_fw">
          <q-input v-model="search.text" placeholder="Search templates...">
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
        </div>

        <div class="__card_grid q-py-md">
          <div v-for="(temp, i) in h$.data || []" :key="`temp-${i}`" class="q-pa-md br10 bs2-5 bg-white cursor-pointer"
               @click="setEditing(temp)">
            <plan-card :model-value="temp" template></plan-card>
          </div>
        </div>

        <common-dialog setting="right" v-model="dialog">
          <div class="_fw q-pa-md">
            <plan-form @new="$router.push({ name: 'console-plan-admin', params: { planId: $event._id } })" template @update:model-value="dialog = false"></plan-form>
          </div>
        </common-dialog>

      </q-tab-panel>
      <q-tab-panel class="_panel" :name="true">

        <plan-generator :model-value="editing"></plan-generator>

      </q-tab-panel>
    </q-tab-panels>

  </q-page>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import PlanForm from 'components/plans/forms/PlanForm.vue';
  import PlanCard from 'components/plans/cards/PlanCard.vue';
  import PlanGenerator from 'components/plans/forms/PlanGenerator.vue';

  import {computed, onMounted, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';

  import {usePlans} from 'stores/plans';
  import {HQuery} from 'src/utils/hQuery';
  import {useRouter, useRoute} from 'vue-router';
  import {useEnvStore} from 'stores/env';

  const route = useRoute();
  const router = useRouter();
  const envStore = useEnvStore();

  const store = usePlans();

  const dialog = ref(false);
  const editing = ref(undefined);

  const { search, searchQ } = HQuery({})
  const { h$ } = HFind({
    store,
    params: computed(() => {
      return {
        query: { ...searchQ.query, template: true }
      }
    })
  })

  const setEditing = (val) => {
    editing.value = val;
    const { href } = router.resolve({ ...route, params: { planId: val._id || val } })
    window.history.pushState({}, '', href)
  }

  onMounted(() => {
    const planId = route.params.planId || envStore.getPlanId;
    if (planId) editing.value = planId;
  })
</script>

<style lang="scss" scoped>

  .__card_grid {
    display: grid;
    width: 100%;
    grid-template-columns: repeat(auto-fill, minmax(300px, 350px));
    grid-template-rows: repeat(auto-fit, auto);
    grid-gap: 10px;
  }
</style>
