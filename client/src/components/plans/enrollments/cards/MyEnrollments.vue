<template>
  <div class="_fw">

    <div class="tw-six q-pt-md text-p6 font-1-1-4r">Plan Enrollments</div>

    <div class="_fw flex items-center q-py-md">
      <q-chip square color="transparent" clickable @click="tab = 'home'">
        <span :class="`${tab === 'home' ? 'tw-six text-primary' : ''}`">Current</span>
      </q-chip>
      <div>|</div>
      <q-chip square dense color="transparent" clickable @click="tab = 'all'">
        <span :class="`${tab === 'all' ? 'tw-six text-primary' : ''}`">History</span>
      </q-chip>
      <div>|</div>
      <q-chip square dense color="transparent" clickable @click="addE">
        <span :class="`${tab === 'add' ? 'tw-six text-primary' : ''}`">Request Change</span>
      </q-chip>
    </div>

    <q-tab-panels class="_panel" v-model="tab" animated transition-prev="fade-out" transition-next="fade-in">
      <q-tab-panel class="_panel" name="home">

        <div class="__c">
        <current-enrollment
            :model-value="current"
        ></current-enrollment>
        </div>

      </q-tab-panel>
      <q-tab-panel class="_panel" name="all">
        <div class="q-px-sm">

          <ul>
            <li class="font-1r"><span class="tw-six">Plan-wide</span> enrollments happen annually at open-enrollment or
              if there are significant changes to the plan.
            </li>
            <li class="font-1r q-pt-sm"><span class="tw-six">Special enrollments</span> can be requested for qualifying
              life-events.
            </li>
            <li class="font-1r q-pt-sm"><span class="tw-six">Election changes</span> for items like HSA can be entered
              anytime.
            </li>
          </ul>

          <div class="row items-center">
            <div>Plan Year:</div>
            <plan-year-picker v-model="year"></plan-year-picker>
          </div>
          <div class="row items-center">
            <status-chip clickable picker multiple v-model="statusFilter"></status-chip>
            <status-chip v-for="(st, i) in statusFilter" :key="`st-${i}`" :model-value="st" removable
                         icon-remove="mdi-close" outline @remove="statusFilter.splice(i, 1)"></status-chip>
          </div>
        </div>
        <div v-if="!e$.total" class="q-pa-sm text-italic">No enrollments found</div>
        <div class="__cg q-px-md q-py-sm">
          <div class="__c" v-for="(ermt, i) in e$.data" :key="`ermt-${i}`">
            <enrollment-card :model-value="ermt"></enrollment-card>
            <spec-list @add="setAdding(ermt)" :enrollment="ermt"></spec-list>
          </div>
        </div>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="add">
        <spec-select @close="tab='home'" :enrollment="adding"></spec-select>
      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import PlanYearPicker from 'components/plans/utils/PlanYearPicker.vue';
  import EnrollmentCard from 'components/plans/enrollments/cards/EnrollmentCard.vue';
  import SpecList from 'components/enrollments/specs/lists/SpecList.vue';
  import SpecSelect from 'components/enrollments/specs/forms/SpecSelect.vue';
  import StatusChip from 'components/enrollments/cards/StatusChip.vue';
  import CurrentEnrollment from 'components/enrollments/cards/CurrentEnrollment.vue';

  import {HFind} from 'src/utils/hFind';
  import {computed, ref, watch} from 'vue';
  import {loginPerson} from 'stores/utils/login';
  import {useEnrollments} from 'stores/enrollments';
  import { getCurrentPlanYear} from 'components/plans/utils';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {useEnvStore} from 'stores/env';

  const { person } = loginPerson()

  const enrollmentsStore = useEnrollments();
  const planStore = usePlans();
  const envStore = useEnvStore();

  const props = defineProps({
    plan: { required: false }
  })

  const tab = ref('home');
  const adding = ref(undefined);
  const statusFilter = ref([]);

  const setAdding = (e) => {
    adding.value = e;
    tab.value = 'add'
  }

  const { item:fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan || envStore.getPlanId),
  })

  const year = ref(new Date().getFullYear().toString())

  const ePause = computed(() => !person.value);
  const { h$:e$ } = HFind({
    store: enrollmentsStore,
    pause: ePause,
    limit: ref(5),
    params: computed(() => {
      const q = {
        $sort: { version: -1 },
        plan: fullPlan.value?._id,
        person: person.value?._id
      };
      if (statusFilter.value?.length) q.status = { $in: statusFilter.value }
      return {
        query: q
      }
    })
  })

  const current = computed(() => e$.data ? e$.data[0] : undefined);

  const addE = () => {
    adding.value = e$.data[0];
    tab.value = 'add'
  }

  watch(fullPlan, (nv, ov) => {
    if(nv && nv._id !== ov?._id) year.value = getCurrentPlanYear(nv)
  }, { immediate: true })

</script>

<style lang="scss" scoped>
  .__cg {
    display: grid;
    grid-template-columns: 100%;
    grid-gap: 20px;
    background: white;
  }

  .__c {
    width: 100%;
    border-radius: 12px;
    box-shadow: 0 5px 12px -2px rgba(99, 99, 99, .3);
    //border: solid 1px black;
    padding: 20px 15px;
  }
</style>
