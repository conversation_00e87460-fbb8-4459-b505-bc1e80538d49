<template>
  <q-page class="row justify-center">
    <div class="pd5 pw4 _fw">
      <docs-display :plan="planId"></docs-display>
    </div>
  </q-page>
</template>

<script setup>

  import DocsDisplay from 'components/plans/docs/cards/DocsDisplay.vue';
  import {computed} from 'vue';
  import {useRoute} from 'vue-router';
  import {useEnvStore} from 'stores/env';

  const envStore = useEnvStore()

  const route = useRoute();

  const planId = computed(() => route.params.planId || envStore.getPlanId)
</script>

<style lang="scss" scoped>

</style>
