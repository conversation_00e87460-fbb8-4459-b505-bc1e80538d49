<template>
  <div class="_fw row justify-center">
    <div class="_cent">

      <div class="__t _top bg-white _oh">
        <org-top :title="`Care Accounts${ca?.name ? ': ' + ca.name : ''}`" :org="fullOrg">
          <template v-slot:title>
            <div class="flex items-center">
              <div class="font-7-8r text-grey-7">Care Accounts</div>
              <template v-if="ca?.name">
                <care-account-picker
                    no-close
                    @update:model-value="setAccount"
                    :org="fullOrg"
                    :model-value="ca"
                ></care-account-picker>
              </template>
            </div>
          </template>
        </org-top>
        <div class="q-py-sm"></div>
        <q-tabs no-caps :model-value="$route.meta?.sub" align="left" v-if="ca?._id && moov_account?.id"
                @update:model-value="setSubRoute">
          <q-tab :label="r" v-for="(r, i) in Object.keys(subRoutes)" :key="`route-${i}`"
                 :name="subRoutes[r].sub"></q-tab>
        </q-tabs>
      </div>

      <div class="_fw q-py-md pw2">
        <div class="q-pa-lg q-px-md" v-if="!ca._id">
      <span class="font-1r">
        <span v-if="!loading">You have not set up your account yet.</span>
        <span class="text-accent tw-six cursor-pointer text-uppercase font-7-8r alt-font q-mx-sm" v-if="!loading"
              @click="setupDialog = true">
          Start one now
        </span>
        <common-dialog setting="right" v-model="setupDialog">
            <div class="_fw q-pa-md">
              <care-account-form @update:model-value="accountId = $event._id"></care-account-form>
            </div>
        </common-dialog>
      </span>
        </div>
        <div v-else-if="moov_account?.verification?.status !== 'verified'">
          <div class="q-pa-sm font-7-8r">Account Status:
            <q-chip clickable color="ir-bg2">
              <span class="q-mr-sm">{{ accountStatus.label }}</span>
              <q-icon v-if="accountStatus.icon" v-bind="accountStatus.icon"></q-icon>
              <span v-else-if="accountStatus.text" v-html="accountStatus.text"></span>
              <q-tooltip class="font-7-8r tw-six">{{ accountStatus.caption }}</q-tooltip>
            </q-chip>
          </div>
          <div class="font-1r">Having trouble or being asked for additional documents?</div>
          <div class="w400 mw100">
          <contact-bar :icon-attrs="{ size: '18px'}"></contact-bar>
          </div>
          <!--          <q-tab-panels class="_panel" v-model="obTab">-->
          <!--            <q-tab-panel class="_panel" name="form">-->
          <account-form :org="fullOrg" v-model="moov_account"></account-form>
          <!--            </q-tab-panel>-->
          <!--            <q-tab-panel class="_panel" name="drop">-->
          <!--              <drop-onboarding :account="moov_account"></drop-onboarding>-->
          <!--            </q-tab-panel>-->
          <!--          </q-tab-panels>-->
        </div>
        <div class="_fw __pd" v-else-if="!accountLoaded">
          <care-account-grid @update:model-value="setAccountId($event._id)"></care-account-grid>
        </div>
        <template v-else>
          <router-view></router-view>
        </template>

      </div>
    </div>
  </div>
</template>

<script setup>
  import OrgTop from 'layouts/components/OrgTop.vue';
  import CareAccountGrid from 'components/care-accounts/lists/CareAccountGrid.vue';
  import CareAccountPicker from 'components/care-accounts/lists/CareAccountPicker.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import AccountForm from 'components/accounts/treasury/components/forms/AccountForm.vue';
  import CareAccountForm from 'components/care-accounts/forms/CareAccountForm.vue';
  import ContactBar from 'components/market/shop/closing/ContactBar.vue';

  import {computed, ref, watch} from 'vue';
  import {useBanking} from 'stores/banking';
  import {useOrgs} from 'stores/orgs';
  import {useCareAccounts} from 'stores/care-accounts';
  import {idGet} from 'src/utils/id-get';
  import {LocalStorage, SessionStorage} from 'symbol-auth-client';
  import {useRoute, useRouter} from 'vue-router';
  import {accountStatuses} from 'components/accounts/treasury/utils';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const orgStore = useOrgs();
  const bankStore = useBanking();
  const caStore = useCareAccounts();

  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const router = useRouter();
  const route = useRoute();


  const accountId = ref();
  const accountLoaded = ref(false);
  // const obTab = ref('form');

  const { item: ca } = idGet({
    store: caStore,
    value: computed(() => accountId.value || LocalStorage.getItem('care_account_id')),
    deepWatch: true,
    onWatch: (item) => {
      if (item.owner && item.owner !== getOrgId.value) {
        LocalStorage.removeItem('care_account_id')
        router.go()
      }
      // if(nv && nv !== ov) router.go()
      setTimeout(() => {
        accountLoaded.value = !!item
      }, 10)
    }
  })
  const { item: fullOrg } = idGet({
    store: orgStore,
    value: getOrgId,
    deepWatch: true,
    onWatch: async (val) => {
      if (val && !LocalStorage.getItem('care_account_id') && !accountId.value) {
        const res = await caStore.find({ query: { owner: val._id, $limit: 1 } })
        if (res.total) {
          LocalStorage.setItem('care_account_id', res.data[0]._id)
          accountId.value = res.data[0]._id
        }
      }
    }
  })
  const setAccountId = (id) => {
    accountId.value = id;
  }
  const treasuryId = computed(() => fullOrg.value?.treasury?.id)

  const setupDialog = ref(false);
  const moov_account = ref(undefined);
  const loading = ref(false);

  const accountStatus = computed(() => {
    const { status, verificationStatus } = moov_account.value?.verification || {}
    return accountStatuses[verificationStatus || status] || {}
  })

  const setAccount = async (val) => {
    if (val) {
      moov_account.value = val;
      SessionStorage.setItem('moov_account', val.accountID);
      // if (val.requirements.currently_due?.length) tab.value = 'form';
      //TODO: rule in or out treasury capability request
      // if (!val.capabilities?.treasury) {
      //   const acct = await bankStore.get(val.id, {
      //     banking: {
      //       moov: {
      //         method: 'account_update',
      //         args: [{ capabilities: { treasury: { requested: true } } }]
      //       }
      //     }
      //   })
      //       .catch(e => console.error(`Could not request treasury permission: ${e.message}`))
      //   if (acct) {
      //     moov_account.value = val;
      //     if (val.requirements.currently_due?.length) tab.value = 'form';
      //   }
      // }
    }
  }

  watch(treasuryId, async (nv) => {
    if (nv && !moov_account.value?.accountID) {
      loading.value = true
      // const url = window.location.href;
      const acct = await bankStore.get(treasuryId.value, {
        banking: {
          moov: {
            method: 'account_get',
            args: [nv]
          }
        }
      });

      if (acct) setAccount(acct);
      else {
        console.log('add new moov acct')
        const newAcct = await caStore.patch(ca.value._id, { $set: { treasury: { id: 'setup' } } })
            .catch(err => console.log(`Error adding moov account to care account: ${err.message}`))
        if (newAcct) router.go();
      }

      loading.value = false;
    }
  }, { immediate: true })

  const subRoutes = computed(() => {
    return {
      'Dash': { link: { name: 'care-account-dash' }, name: 'care-account-dash', sub: 'dash' },
      // 'Cards': { link: { name: 'care-account-cards' }, name: 'org-care-cards' },
      'Budgets': { link: { name: 'org-budgets' }, name: 'org-budgets', sub: 'budgets' },
      'Settings': { link: { name: 'care-account-settings' }, name: 'org-care-settings', sub: 'settings' }
    }
  })
  const setSubRoute = (metaName) => {
    for (const k in subRoutes.value) {
      if (subRoutes.value[k].sub === metaName) {
        router.push({ ...route, ...subRoutes.value[k].link, params: route.params });
        break;
      }
    }
  }

  watch(moov_account, (nv, ov) => {
    if (nv && nv.accountID !== ov?.accountID) {
      setTimeout(async () => {
        if (nv.accountID && ca.value._id && !ca.value.moov_id) {
          if (nv.profile.business.legalBusinessName === fullOrg.value.legalName || nv.profile.business.legalBusinessName === fullOrg.value.name) {
            console.log('add moov id')
            await caStore.patch(ca.value._id, { moov_id: nv.accountID })
            router.go()
          }
        }
      }, 3000)
    }
  })

</script>

<style lang="scss" scoped>
  .__t {
    padding: 3vh 3vw 0 3vw;
  }

  .__pd {
    padding: 20px min(3vw, 20px);
  }

</style>
