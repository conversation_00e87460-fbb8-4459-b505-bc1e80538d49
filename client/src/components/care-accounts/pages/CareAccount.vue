<template>
  <div class="_fw">

    <div class="__c" v-if="care_account?._id">
      <div class="__r">
        <q-input placeholder="Name Account..." input-class="tw-six font-1-1-4r" borderless
                 :model-value="care_account?.name" @update:model-value="updateName">
          <template v-slot:prepend>
            <q-img class="w20 h20" :src="icon" fit="contain"></q-img>
          </template>
        </q-input>
        <div class="row q-pb-sm">
          <div class="flex items-center q-px-sm">
            <div class="font-1r tw-six alt-font">Spend Limit:</div>

            <q-chip square clickable color="p0">
              <!--              <q-icon size="18px" color="p3" class="q-mr-sm" name="mdi-target"></q-icon>-->
              <span class="tw-seven font-1-1-4r text-p12">{{ dollarString((care_account?.amount || 0) / 100, '$', 0) }}</span>
              <q-popup-proxy breakpoint="50000">
                <div class="w300 mw100 q-pa-lg bg-white">
                  <div class="row items-center">
                    <q-icon size="18px" color="p3" class="q-mr-sm" name="mdi-target"></q-icon>
                    <span class="tw-six text-primary font-1r">Balance Limit</span>
                  </div>
                  <q-separator class="q-my-sm"></q-separator>
                  <div class="font-7-8r">The balance that account budgets will be held to.</div>
                  <money-input
                      no-error-icon
                      :model-value="(balanceInput.amount || care_account?.amount)/100"
                      prefix="$"
                      input-class="tw-sx font-1-1-8r"
                      @update:model-value="updateBalance($event, 'amount')"
                      :error="errs.recurs"
                      :hint="messages.amount"
                      :error-message="messages.amount"
                  ></money-input>
                  <div class="row justify-end q-px-md q-pt-sm">
                    <q-btn flat no-caps @click="saveBalance('amount')" v-if="(balanceInput.amount !== care_account?.amount) && !errs.amount">
                      <span class="tw-six text-grey-7 q-mr-sm">Save</span>
                      <q-icon color="primary" name="mdi-content-save"></q-icon>
                    </q-btn>
                  </div>
                </div>
              </q-popup-proxy>
            </q-chip>
          </div>

          <div class="flex items-center q-px-sm">
            <div class="font-1r tw-six alt-font">Monthly Limit:&nbsp;</div>
            <q-chip square clickable color="p0">
              <!--              <q-icon size="18px" color="a3" class="q-mr-sm" name="mdi-calendar"></q-icon>-->
              <span
                  class="tw-six font-1-1-4r text-p12">{{ dollarString((care_account?.recurs || 0) / 100, '$', 0) }} /mo</span>

              <q-popup-proxy breakpoint="50000">
                <div class="w300 mw100 q-pa-lg bg-white">
                  <div class="row items-center">
                    <q-icon size="18px" color="a3" class="q-mr-sm" name="mdi-calendar"></q-icon>
                    <span class="tw-six text-accent font-1r">Monthly Spend Limit</span>
                  </div>
                  <q-separator class="q-my-sm"></q-separator>
                  <div class="font-7-8r">The balance your recurring budgets will be held to each month. Forecasted budgets
                    will be loosely enforced - and then strongly once the new month begins.
                  </div>
                  <money-input
                      no-error-icon
                      :model-value="(balanceInput.recurs || care_account?.recurs)/100"
                      prefix="$"
                      input-class="tw-sx font-1-1-8r"
                      @update:model-value="updateBalance($event, 'recurs')"
                      :error="errs.recurs"
                      :hint="messages.recurs"
                      :error-message="messages.recurs"
                  ></money-input>
                  <div class="row justify-end q-px-md q-pt-sm">
                    <q-btn flat no-caps @click="saveBalance('recurs')" v-if="(balanceInput.recurs !== care_account?.recurs) && !errs.recurs">
                      <span class="tw-six text-grey-7 q-mr-sm">Save</span>
                      <q-icon color="accent" name="mdi-content-save"></q-icon>
                    </q-btn>
                  </div>
                </div>
              </q-popup-proxy>
            </q-chip>
          </div>

        </div>
      </div>

      <div v-for="(k, i) in Object.keys(warn)" :key="`kw-${i}`" :class="`__banner bg-${warn[k].color }`">
        {{warn[k].message}}
      </div>
      <treasury-dash :org="fullOrg" :model-value="care_account"></treasury-dash>
    </div>
    <div v-else class="q-pa-xl">
      <q-spinner size="30px" color="primary"></q-spinner>
    </div>
  </div>
</template>

<script setup>
  import icon from 'src/assets/common_cent_grey.svg'
  import TreasuryDash from 'components/accounts/treasury/components/cards/TreasuryDash.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';

  import {computed, ref, watch} from 'vue';
  import {LocalStorage, SessionStorage} from 'symbol-auth-client';
  import {useBanking} from 'stores/banking';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {capabilities_list, walletBalance} from 'src/components/accounts/treasury/utils';
  import {useCareAccounts} from 'stores/care-accounts';
  import {dollarString} from 'src/utils/global-methods';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const bankStore = useBanking();
  const orgStore = useOrgs();
  const caStore = useCareAccounts();

  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const balanceInput = ref({ amount: 0, recurs: 0 })

  const { item: care_account } = idGet({
    store: caStore,
    value: computed(() => LocalStorage.getItem('care_account_id')),
    params: ref({ runJoin: { with_wallet: true } }),
    refreshOn: (val) => !val?._fastjoin?.account,
    onWatch: (val) => {
      balanceInput.value.amount = val.amount || 0;
      balanceInput.value.recurs = val.recurs || 0;
    }
  })

  const account = computed(() => care_account.value?._fastjoin?.account)
  const wallet = computed(() => care_account.value?._fastjoin?.wallet)

  const { item: fullOrg } = idGet({
    store: orgStore,
    value: getOrgId
  })

  //TODO: watch for freeze and run banking: { sync_accounts:true } to check balances again if the account balance doesn't match the lastSync balance
  const warn = computed(() => {
    const cv = care_account.value;
    if (!cv) return {};
    else {
      const { lastSync = { freeze: false } } = cv;
      const obj = {};
      if (lastSync.freeze) obj.freeze = { message: `Budgets frozen. You have ${dollarString((lastSync.excess || 0) / 100, '$', 0)} more committed to your budgets than account balance`, color: 'red' }
      if(lastSync.excess) obj.excess = { message: `Your assigned funds exceed your target account balance by ${dollarString(lastSync.adjust_amount, '$', 0)}. Recommend you adjust budgets or target.`, color: 'accent'}
      return obj
    }
  })

  const to = ref();
  const updateName = (val) => {
    const id = care_account.value._id;
    caStore.patchInStore(id, { name: val })
    if (to.value) clearTimeout(to.value);
    to.value = setTimeout(() => {
      caStore.patch(id, { name: val })
    }, 2000);
  }

  const messages = computed(() => {
    return {
      amount: `You have ${dollarString((care_account.value?.assigned_amount || 0) / 100, '$', 0)} commited to sub-budgets`,
      recurs: `You have ${dollarString((care_account.value?.assigned_recurs || 0) / 100, '$', 0)} commited to recurring sub-budgets`
    }
  })
  const errs = ref({ recurs: false, amount: false })

  const updateBalance = (val, path) => {
    const v = val * 100
    balanceInput.value[path] = v
    errs.value[path] = care_account.value[`assigned_${path}`] > v;

  }
  const saveBalance = (path) => {
    if (!errs.value[path]) {
      const v = balanceInput.value[path];
      const id = care_account.value._id;
      caStore.patchInStore(id, { [path]: v })
      caStore.patch(id, { [path]: v })
    }
  }

  const checkFreeze = async () => {
    const { freeze, balance } = care_account.value?.lastSync || {};
    if(freeze){
      const newBalance = walletBalance(wallet.value);
      if(newBalance !== balance){
        await caStore.get(care_account.value._id, { banking: { sync_budgets: true } })
            .catch(err => {
              console.error(`Error syncing budgets on frozen account: ${err.message}`)
            })
            .then(() => {
              console.log('Successfully checked sync budget on account freeze')
            })
      }
    }
  }

  watch(account, async (nv) => {
    if (nv) {
      // const last4 = (nv.financial_addresses || [])[0]?.aba?.account_number_last4
      // if (!care_account.value.last4 && last4) caStore.patch(care_account.value._id, { last4 })
      if (nv.walletID && !nv?.capabilities?.includes('transfers')) {
        const list = [];
        for (const k of capabilities_list) {
          if (!nv.capabilities?.includes(k)) list.push(k)
        }
        if (list.length) {
          setTimeout(async () => {
            const connect_id = SessionStorage.getItem('moov_account');
            const newFa = await bankStore.get(connect_id, {
              banking: {
                stripe: {
                  method: 'add_capabilities',
                  args: [nv.id, { caps: list }]
                }
              }
            })
                .catch(err => {
                  console.error(`Error requesting intra flows: ${err.message}`);
                  return nv;
                })
            if(newFa) {
              caStore.patchInStore(care_account.value._id, {
                _fastjoin: {
                  ...care_account.value._fastjoin,
                  account: newFa
                }
              })
              if (newFa.verification?.status && care_account.value.status !== newFa.status) {
                console.log('call ca store')
                await caStore.patch(care_account.value._id, { status: newFa.verification.status })
              }
            }
          }, 200)
        }
        if(warn.value?.freeze){
          checkFreeze()
        }
      }
    }
  }, { immediate: true })


</script>

<style lang="scss" scoped>
  .__c {
    padding: min(20px, 3vh) max(20px, 3vw);
    border-radius: 10px;
    //box-shadow: 0 2px 8px -3px #999;
  }

  .__r {
    padding: 10px min(20px, 3vw);
    border-radius: 6px;
    box-shadow: 0 2px 6px -4px #999;
    background: linear-gradient(-8deg, #f0f0f0, white, #f0f0f0);
    //background: linear-gradient(-8deg, var(--q-p10), var(--q-p9), var(--q-p10));
    margin-bottom: 20px;
  }
  .__banner {
    padding: 6px 15px;
    border-radius: 5px;
    box-shadow: 0 2px 4px #999;
    font-size: .85rem;
    color: white;
    font-weight: 600;
    margin: 5px 0;
  }
</style>
