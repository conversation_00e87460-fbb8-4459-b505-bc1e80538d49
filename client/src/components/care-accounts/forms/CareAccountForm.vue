<template>
  <div class="_fw relative-position">
    <div :class="`__loader flex flex-center ${loading ? '' : '__off'}`">
      <div>
        <div class="row justify-center q-pa-md">
          <ai-logo opaque></ai-logo>
        </div>
        <div class="q-pa-md text-center tw-six font-1r">Provisioning Account...</div>
      </div>
    </div>
    <!--    <template v-if="orgReady.ready">-->
    <div class="q-pa-md tw-six font-1r text-ir-deep">{{ form?._id ? 'Edit' : 'Add' }} Plan Wallet</div>
    <div class="_form_grid _f_g_r">
      <div class="_form_label">Account Owner</div>
      <div class="q-pa-sm">
        <default-chip :model-value="org"></default-chip>
      </div>
      <div class="_form_label">Account Name</div>
      <div class="q-pa-sm">
        <q-input input-class="font-1-1-4r tw-six" dense filled v-model="form.name" @blur="autoSave('name')"></q-input>
      </div>

    </div>
    <template v-if="!accepted && form.name?.length > 3">
      <tos-accept @accept="acceptTos" :manual-tos="manualTos" @update:manual-tos="handleManual" v-model:account="account" @update:tos-token="handleToken"></tos-accept>
    </template>

    <!--    </template>-->
    <q-slide-transition>
      <div class="_fw" v-if="!orgReady.ready">
        <div class="q-pa-sm tw-five font-1r">
          We need a bit more detail about your company for banking requirements
          <div class="text-secondary tw-six font-7-8r">Info Needed:
            {{ Object.keys(orgReady).filter(a => !orgReady[a] && a !== 'ready').join(', ') }}
          </div>
        </div>
        <org-form no-logo full :model-value="org"></org-form>
      </div>
    </q-slide-transition>
  </div>
</template>

<script setup>
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import OrgForm from 'components/orgs/forms/OrgForm.vue';
  import TosAccept from 'components/care-accounts/utils/TosAccept.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';

  import {HForm, HSave} from 'src/utils/hForm';
  import {useCareAccounts} from 'stores/care-accounts';
  import {LocalStorage} from 'symbol-auth-client';
  import {computed, ref, watch} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {useBanking} from 'stores/banking';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const caStore = useCareAccounts();
  const orgStore = useOrgs();
  const bankStore = useBanking();

  const envStore = useEnvStore();
  const { getOrgId:orgId } = contextItems(envStore);

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: false }
  })

  const account = ref({})
  const accepted = computed(() => account.value?.termsOfService?.acceptedDate)

  const { item: org } = idGet({
    store: orgStore,
    value: orgId
  })

  const tosToken = ref()
  const manualTos = ref({});

  const orgReady = computed(() => {
    const { phone, email, legalName, address } = org.value || {}
    return {
      phone: !!phone,
      email: !!email,
      legalName: !!legalName,
      address: !!address,
      ready: !!(phone && email && legalName && address)
    }
  })

  const loading = ref(false);

  const { form, save } = HForm({
    store: caStore,
    validate: true,
    log: false,
    onWatch: (val) => {
      if(val?._fastjoin?.account) account.value = val._fastjoin.account;
    },
    vOpts: ref({ name: { name: 'name', v: ['notEmpty'] } }),
    value: computed(() => props.modelValue),
    params: computed(() => {
      return {
        runJoin: { with_wallet: true },
        banking: {
          account_data: { termsOfService: tosToken.value?.token ? tosToken.value : manualTos.value }
        }
      }
    }),
    beforeFn: (item) => {
      console.log('item', item);
      loading.value = true
      if (!item.owner) item.owner = orgId.value
      return item;
    },
    afterFn: async (val) => {
      console.log('after fn', val);
      loading.value = true;
      if(val._fastjoin?.account) {
        account.value = val._fastjoin.account;
        /** The accounts are not able to be created with TOS acceptance as far as I can tell because the accounts/${accountID}/profile.write scope is not available on account create. This will patch the account after generating a proper token for the account */
        if(!accepted.value) {
          const token = await bankStore.get(account.value.accountID, {
            banking: {
              moov: {
                method: 'get_tos_token',
                args: []
              }
            }
          }).catch(err => console.error(`Error getting tos token: ${err.message}`))

          tosToken.value = token;
          account.value = await bankStore.get(account.value.accountID, {
            banking: {
              moov: {
                method: 'account_update',
                args: [{ termsOfService: token?.token ? token : manualTos.value }]
                // args: [{ termsOfService: token?.token ? token : manualTos.value }]
              }
            }
          })
              .catch(err => console.error(`Error updating account with TOS token: ${err.message}`))

          loading.value = false;
        }
      }
      emit('update:model-value', val);
    }
  })

  const acceptTos = (v) => {
    console.log('accept tos', v);
    if(!form.value._id) save()
  }

  const handleToken = (val) => {
    tosToken.value = val;
  }

  const handleManual = (val) => {
    manualTos.value = val;
  }

  const { autoSave } = HSave({ form, store: caStore, pause: computed(() => !form.value._id) })

  watch(() => props.modelValue, async (nv, ov) => {
    if(nv && nv.moov_id !== ov?.moov_id){
      if(nv._fastjoin?.account) account.value = nv._fastjoin.account;
      else {
        const moov_account = await bankStore.get(nv.moov_id, {
          banking: {
            moov: {
              method: 'get_account',
              args: []
            }
          }
        })
            .catch(err => console.error(`Error getting account: ${err.message}`))
        if(moov_account) account.value = moov_account;
      }
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>

  .__loader {
    transition: all .3s;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,.5);
    z-index: 10;
    backdrop-filter: blur(5px);

  }
  .__off {
    padding: 0;
    height: 0;
    width: 0;
    opacity: 0;
    pointer-events: none;
  }
  //:root {
  //  --moov-color-background: #111111;
  //  --moov-color-background-secondary: #222222;
  //  --moov-color-background-tertiary: #333333;
  //  --moov-color-primary: #77D656;
  //  --moov-color-secondary: #79F0AA;
  //  --moov-color-tertiary: #4F4F4F;
  //  --moov-color-info: #94CBFF;
  //  --moov-color-warn: #F2994A;
  //  --moov-color-danger: #EB5757;
  //  --moov-color-success: #77D656;
  //  --moov-color-low-contrast: #969696;
  //  --moov-color-medium-contrast: #E0E0E0;
  //  --moov-color-high-contrast: #FFFFFF;
  //  --moov-color-graphic-1: #79F0AA;
  //  --moov-color-graphic-2: #D194E9;
  //  --moov-color-graphic-3: #68B2FD;
  //  --moov-radius-small: .375rem;
  //  --moov-radius-large: .5rem;
  //}
</style>
