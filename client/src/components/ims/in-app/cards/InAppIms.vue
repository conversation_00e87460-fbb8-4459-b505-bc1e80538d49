<template>
  <div class="__ims">
    <div class="_fw">
      <div class="_fw">
        <div class="font-3-4r tw-six q-pa-sm">Continue this via text or email</div>
        <div class="q-pb-sm">
          <email-input-chip
              clickable
              @click="setParticipantContact('email')"
              icon-color="accent"
              color="transparent"
              v-model="form.participant.email"
              @update:model-value="setParticipantContact('email')"
          ></email-input-chip>
          <phone-input-chip
              emit-value
              option
              clickable
              @click="setParticipantContact('phone')"
              color="transparent"
              :model-value="form.participant.phone"
              @update:model-value="setParticipantContact('phone', $event)"
          ></phone-input-chip>
        </div>
      </div>
      <q-separator class="q-mb-sm" color="ir-light"></q-separator>
      <div v-if="tab === 'base'" class="__human">We ❤️ AI, but not here. Our support is 100% human - and no call
        centers.
      </div>

    </div>

    <q-tab-panels keep-alive class="_panel _fh" animated :model-value="tab">
      <q-tab-panel class="_panel __os" name="base">

        <div class="font-3-4r tw-six q-pt-md text-ir-text">Chat History</div>
        <q-separator class="q-my-xs" color="ir-light"></q-separator>
        <q-list separator>
          <q-item dense v-if="i$.isPending || !i$.total">
            <q-item-section avatar v-if="i$.isPending">
              <ai-logo opaque></ai-logo>
            </q-item-section>
            <q-item-section>
              <q-item-label>
                {{ i$.isPending ? 'Searching chats...' : 'No chats found - ' }}
                <span class="cursor-pointer text-primary tw-six">Start a new one</span>
              </q-item-label>
            </q-item-section>
          </q-item>

          <im-item v-for="(iM, i) in i$.data" :key="`im-${i}`" :model-value="iM" clickable
                   @click="setIm(iM._id)"></im-item>

          <q-item clickable @click="toggleAdd(true)">
            <q-item-section avatar>
              <q-icon color="primary" name="mdi-plus"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label class="tw-six font-7-8r text-ir-mid">New chat</q-item-label>
            </q-item-section>
          </q-item>

        </q-list>
      </q-tab-panel>
      <q-tab-panel class="_panel __os" name="add">
        <div class="_fw">
          <q-btn no-caps rounded dense flat size="sm" @click="adding = false">
            <q-icon name="mdi-chevron-left" color="primary" class="q-mr-xs"></q-icon>
            <span>Menu</span>
          </q-btn>
        </div>

        <q-input borderless placeholder="Your Name" dense required v-model="form.participant.name" filled
                 class="q-mt-sm"></q-input>
        <q-separator class="q-mt-sm" color="ir-bg"></q-separator>
        <q-input autogrow placeholder="Start the conversation..." filled borderless v-model="message.body"></q-input>

        <q-slide-transition>
          <div class="row justify-center q-pt-md" v-if="!notabot">
            <div class="mw100 q-pa-sm">
              <turnstile-widget interactive v-model="notabot"></turnstile-widget>
            </div>
          </div>
        </q-slide-transition>
        <div class="q-pt-md row justify-end">
          <q-btn glossy :disable="!notabot || !form.participant.name" color="primary" size="sm" push class="tw-six"
                 label="Start Chat" icon-right="mdi-chevron-right" @click="startChat"></q-btn>
        </div>
      </q-tab-panel>
      <q-tab-panel class="_panel _fh" name="chat">
        <div class="__chat">
          <div class="_fw row items-center">
            <q-btn no-caps rounded dense flat size="sm" @click="resetIm">
              <q-icon name="mdi-chevron-left" color="primary" class="q-mr-xs"></q-icon>
              <span>Menu</span>
            </q-btn>
            <q-space></q-space>
            <div class="text-xxxs tw-six text-ir-deep">{{$possiblyPlural('message', im?.messages)}}</div>
          </div>
          <im-chat :pid="pid" :notabot="notabot" :dark="dark" :model-value="im"></im-chat>
          <div class="_fw">
            <q-slide-transition>
              <div class="row justify-center q-pt-md" v-if="!notabot">
                <div class="mw100 q-pa-sm">
                  <turnstile-widget interactive v-model="notabot"></turnstile-widget>
                </div>
              </div>
            </q-slide-transition>
          </div>
        </div>
      </q-tab-panel>
    </q-tab-panels>
    <div class="q-pt-sm">
      <text-us-chip @open="openDirect('texting')" color="ir-bg2" text-color="ir-text"></text-us-chip>
      <call-us-chip @open="openDirect('calling')" color="ir-bg2" text-color="ir-text"></call-us-chip>
    </div>
  </div>
</template>

<script setup>
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import TurnstileWidget from 'src/utils/turnstile/TurnstileWidget.vue';
  import EmailInputChip from 'components/ims/in-app/utils/EmailInputChip.vue';
  import PhoneInputChip from 'components/ims/in-app/utils/PhoneInputChip.vue';
  import ImItem from 'components/ims/in-app/cards/ImItem.vue';
  import ImChat from 'components/ims/in-app/cards/ImChat.vue';
  import TextUsChip from 'components/ims/text/utils/TextUsChip.vue';
  import CallUsChip from 'components/ims/utils/CallUsChip.vue';

  import {loginPerson} from 'stores/utils/login';
  import {computed, onMounted, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useIms} from 'stores/ims';
  import {LocalStorage, SessionStorage} from 'symbol-auth-client';
  import {idGet} from 'src/utils/id-get';
  import {_get, _set} from 'symbol-syntax-utils';
  import {$errNotify, $possiblyPlural} from 'src/utils/global-methods';
  import {useEnvStore} from 'stores/env';
  import {storeToRefs} from 'pinia';
  import { msg } from '../utils'

  const envStore = useEnvStore();
  const { dark } = storeToRefs(envStore);

  const { person, isAuthenticated, login } = loginPerson();
  const imStore = useIms();

  const adding = ref(false);
  const notabot = ref(false);

  const imId = ref();
  const { item: im } = idGet({
    store: imStore,
    value: imId
  })

  const pid = computed(() => login.value._id || LocalStorage.getItem('fpId'))

  const resetIm = () => {
    imId.value = undefined;
    SessionStorage.removeItem('im_id');
  }

  const params = computed(() => {
    return {
      plan: envStore.getPlanId,
      host: envStore.getHostId
    }
  })

  const genForm = (val) => {
    const obj = { participant: { sendTo: 'in-app', login: login.value._id, fp: LocalStorage.getItem('fpId') } };
    const adders = {
      plan: envStore.getPlanId,
      subject: SessionStorage.getItem('im_subject'),
      // subjectService: SessionStorage.getItem('im_subject_service'),
      session_fp: LocalStorage.getItem('fpId'),
    }
    for (const k in adders) {
      if (adders[k]) obj[k] = adders[k];
    }
    // console.log('set form', obj, val)
    return {
      ...obj,
      ...val,
      participant: {
        ...obj.participant,
        ...val?.participant
      },
    }
  }
  const form = ref(genForm());


  const message = ref(msg({ pid: pid.value }));

  const setIm = (id) => {
    SessionStorage.setItem('im_id', id)
    imId.value = id;
    adding.value = false;
  }
  const startChat = async () => {

    if (message.value.body) form.value.messages = [{ pid: pid.value, ...message.value }];
    const v = genForm(form.value)
    const newIm = await imStore.create(v, params.value)
        .catch(err => {
          $errNotify(`Error opening chat: ${err.message}`)
          console.error(`Error opening chat: ${err.message}`)
        })
    if (newIm) {
      setIm(newIm._id)
      message.value = msg({ pid: pid.value });
    }
  }

  const openDirect = async (path) => {
    form.value[path] = true;
    form.value.participant.sendTo = 'phone'
    const v = genForm(form.value);
    const newIms = await imStore.create(v, params.value)
    if(newIms) SessionStorage.setItem('ims_id', newIms._id);

  }

  const patchObj = ref({ $set: {} });
  const saveTo = ref();
  const autoSave = (path) => {
    if (im.value?._id) {
      patchObj.value.$set = _set(patchObj.value.$set, path, _get(form.value, path))
      if (saveTo.value) clearTimeout(saveTo.value);
      saveTo.value = setTimeout(async () => {
        const patched = await imStore.patch(im.value._id, patchObj.value, params.value)
        if (patched) patchObj.value = { $set: {} }
      }, 2000)
    }
  }

  const setParticipantContact = (path, val) => {
    if(val) form.value.participant[path] = val;
    if(form.value.participant[path]) {
      form.value.participant.sendTo = path;
      autoSave(`participant.${path}`)
    }
  }

  const tab = computed(() => adding.value ? 'add' : imId.value ? 'chat' : 'base')

  const { h$: i$ } = HFind({
    store: imStore,
    limit: ref(5),
    params: computed(() => {
      const query = { $sort: { updatedAt: -1 } }
      if (isAuthenticated.value) query.person = person.value._id;
      else {
        const fpId = LocalStorage.getItem('fpId');
        if (fpId) query.session_fp = fpId;
        else query.session_fp = '**_**'
      }
      return { query }
    })
  })

  const toggleAdd = (v) => {
    adding.value = v;
    if (v) {
      if (person.value) {
        form.value = genForm({
          person: person.value._id,
          participant: {
            name: person.value.name,
            email: person.value.email,
            phone: person.value.phone?.number.e164,
            login: person.value.login
          }
        })
      } else {
        let p
        for(let i = 0; i < i$.data.length; i++){
          if(i$.data[i].participant?.name){
            p = i$.data[i].participant
            break;
          }
        }
        if(p) form.value = genForm({
          participant: p
        })
      }
    }
  }

  onMounted(() => {
    imId.value = SessionStorage.getItem('im_id');
  })

</script>

<style lang="scss" scoped>
  .__ims {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-rows: auto 1fr auto;
    overflow: hidden;
  }

  .__human {
    font-size: .8rem;
    font-weight: 500;
    padding: 10px;
    margin-bottom: 3px;
    border-radius: 8px;
    background: var(--ir-bg-grad);
    color: var(--ir-deep);
  }

  .__chat {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-rows: auto 1fr auto;

    > div {
      &:nth-child(2) {
        align-content: end;
        height: 100%;
        overflow: hidden;
      }
    }
  }

  .__os {
    height: 100%;
    overflow-y: scroll;
  }
</style>
