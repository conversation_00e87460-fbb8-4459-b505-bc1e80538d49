<template>
  <div class="_fw">
    <div class="row justify-center pd4 pw2">
      <div class="_sent">
        <div class="row justify-center">
          <div class="_fw pw2 pd5">
            <div class="text-center tw-five text-md alt-font"><span class="text-primary">Common</span><span
                class="text-secondary">Rx</span> open-source drug pricing
            </div>
            <div class="text-center text-xxl text-ir-text tw-five q-py-sm">Lowest known price for any drug</div>
            <div class="text-center text-lg text-ir-text alt-font text-primary">+ how you can get that price
            </div>

            <div class="row justify-center q-py-md">
              <div class="_fw mw500">
                <q-separator></q-separator>
              </div>
            </div>
            <div class="row justify-center items-center">
              <q-chip square size="sm" clickable @click="tab = 'search'" color="transparent">
                <q-icon name="mdi-magnify" color="accent" class="q-mr-sm text-sm"></q-icon>
                <span :class="`tw-${tab === 'search' ? 'six' : 'four'} text-xxs`">Single</span>
              </q-chip>
              <div>|</div>
              <q-chip square size="sm" clickable @click="tab = 'bulk'" color="transparent">
                <span :class="`tw-${tab === 'bulk' ? 'six' : 'four'} text-xxs`">Bulk</span>
                <q-icon name="mdi-file-delimited" color="green" class="q-ml-sm text-sm"></q-icon>

              </q-chip>
            </div>

            <div class="_fw q-pt-lg">
              <q-tab-panels keep-alive v-if="notabot || isAuthenticated" v-model="tab" class="_panel" animated>
                <q-tab-panel class="_panel" name="search">


                  <div class="row justify-center">
                    <div class="__srch" v-if="!ndcMed">
                      <meds-list @ndc="setNdc" @focus="focused = true" @blur="focused = false" ndc v-model="med">
                        <template v-slot:input="scope">
                          <div class="__inp _fw">
                            <q-input @focus="scope.setFocus" @blur="scope.setBlur" placeholder="Enter drug name or code"
                                     borderless :model-value="scope.search.text" @update:model-value="scope.setSearch">
                              <template v-slot:prepend>
                                <q-icon name="mdi-pill"></q-icon>
                              </template>
                            </q-input>
                          </div>
                        </template>
                      </meds-list>
                    </div>
                    <div v-else class="_fw mw600">
                      <med-item :model-value="ndcMed">
                        <template v-slot:side>
                          <q-btn size="sm" dense flat color="red" icon="mdi-close"
                                 @click="ndcMed = undefined, med = undefined"></q-btn>
                        </template>
                      </med-item>
                    </div>
                  </div>

                  <q-slide-transition>
                    <div class="row justify-center" v-if="ndcMed">
                      <div class="_xsent __res">

                        <div class="row">
                          <div class="col-12 col-md-6 q-py-md pw1">
                            <div class="q-pa-sm text-center">
                              <div class="text-xs tw-six text-ir-deep">Exact Matches</div>
                              <div class="text-xxs text-ir-deep" v-if="!prices.total">No exact matches found</div>
                            </div>
                            <ai-logo v-if="loading" opaque size="80px" dark></ai-logo>
                            <div class="__list">
                              <q-list separator>
                                <q-item v-for="(p, i) in prices.data" :key="`pr-${i}`" clickable>
                                  <q-item-section>
                                    <q-item-label class="tw-six font-3-4r text-primary">{{
                                        p.providerName || ''
                                      }}
                                    </q-item-label>
                                    <q-item-label>{{ p.name }}</q-item-label>
                                    <q-item-label caption>{{ p.s_f || p.description }}</q-item-label>
                                  </q-item-section>
                                  <q-item-section side>
                                    <div class="tw-six font-1r">{{ dollarString(p.price / 100, '$', 2) }}</div>
                                  </q-item-section>
                                </q-item>
                              </q-list>
                            </div>
                          </div>
                          <div class="col-12 col-md-6 q-py-md pw1">
                            <div class="q-pa-sm text-center">
                              <div class="text-xs tw-six text-ir-deep">Likely Matches</div>
                              <div class="text-xxs text-ir-deep" v-if="!close.total">No close matches found</div>
                            </div>

                            <div class="__list">
                              <q-list separator>
                                <q-item v-for="(p, i) in close.data" :key="`cl-${i}`" clickable>
                                  <q-item-section>
                                    <q-item-label class="tw-six font-3-4r text-primary">{{
                                        p.providerName || ''
                                      }}
                                    </q-item-label>
                                    <q-item-label>{{ p.name }}</q-item-label>
                                    <q-item-label caption>{{ p.s_f || p.description }}</q-item-label>
                                  </q-item-section>
                                  <q-item-section side>
                                    <div class="tw-six font-1r">{{ dollarString(p.price / 100, '$', 2) }}</div>
                                  </q-item-section>
                                </q-item>
                              </q-list>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </q-slide-transition>

                </q-tab-panel>

                <!--                BULK-->
                <q-tab-panel class="_panel" name="bulk">


                  <bulk-price-table :id="session._id" @update:id="setId"></bulk-price-table>


                </q-tab-panel>
              </q-tab-panels>
              <div class="row justify-center" v-else>
                <div class="q-pa-lg">
                  <turnstile-widget interactive v-model="notabot"></turnstile-widget>
                </div>
              </div>
            </div>

          </div>
        </div>

      </div>
    </div>

    <div class="row justify-center pd12">
      <div class="_cent pw2">
        <div class="row">
          <div class="col-12 col-md-6 q-py-md pw2">
            <div class="text-ir-text text-xl tw-five">How do you get this pricing?
            </div>
            <div class="text-md text-primary alt-font q-py-sm">Well, often you can't - but your doctor can.
            </div>

            <div class="row q-py-lg">
              <q-btn rounded color="primary" no-caps class="tw-five text-sm">
                <span>Learn about direct care</span>
              </q-btn>
            </div>
          </div>
          <div class="col-12 col-md-6 q-py-md pw2">

            <div class="__lists">
              <div>Instead of</div>
              <div class="__list">
                <div v-for="(item, i) in inteadOf.items" :key="`io-${i}`">
                  <div>
                    <q-icon :name="item[1]" color="ir-deep"></q-icon>
                  </div>
                  <div>{{ item[0] }}</div>
                </div>
              </div>
            </div>

            <div class="__lists">
              <div>You get</div>
              <div class="__list">
                <div v-for="(item, i) in youGet.items" :key="`yg-${i}`">
                  <div>
                    <q-icon :name="item[1]" color="primary"></q-icon>
                  </div>
                  <div>{{ item[0] }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>



      </div>

      <div class="row justify-center pd12">
        <div class="_cent pw4">
          <div class="text-md tw-five">A more thoughtful approach improves more than price:</div>
          <div class="text-sm">
            Pharma is complex because prescription meds have a powerful acute effect on your body's chemistry. Drugs aren't sneakers. Distribution has a lot of red tape. Your doctor can cut through that for you. Practicing medicine can include using powerful meds when strong intervention is needed, but there are always trade-offs to manage. That's why having your doctor manage your meds actively and directly is the most sensible path. It's also why the complexities to sell to a physician are significantly less.
          </div>
        </div>
      </div>
    </div>

    <turnstile-popup
        v-if="!isAuthenticated && !notabot"
        v-model:interactive="showTurnstile"
        v-model="notabot"
    ></turnstile-popup>
  </div>
</template>

<script setup>
  import MedsList from 'components/care/meds/lists/MedsList.vue';
  import MedItem from 'components/care/meds/cards/MedItem.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import TurnstilePopup from 'src/utils/turnstile/TurnstilePopup.vue';
  import TurnstileWidget from 'src/utils/turnstile/TurnstileWidget.vue';
  import BulkPriceTable from 'components/care/meds/utils/BulkPriceTable.vue';

  import {computed, ref} from 'vue';
  import {usePrices} from 'stores/prices';
  import {dollarString} from 'symbol-syntax-utils';
  import {getRegexQuery} from 'src/utils/hQuery';
  import {loginPerson} from 'stores/utils/login';
  import {useBillErasers} from 'stores/bill-erasers';
  import {idGet} from 'src/utils/id-get';
  import {LocalStorage, SessionStorage} from 'symbol-auth-client';
  import {useRoute, useRouter} from 'vue-router';
  import {useEnvStore} from 'stores/env';

  const route = useRoute();
  const router = useRouter();

  const eraserStore = useBillErasers()
  const envStore = useEnvStore()

  const { isAuthenticated, person } = loginPerson()
  const notabot = ref(false);
  const showTurnstile = ref(false);

  const priceStore = usePrices();
  const tab = ref('search')

  const id = ref()
  const { item: session } = idGet({
    store: eraserStore,
    value: computed(() => id.value || route.params.sessionId || SessionStorage.getItem('rx_session')),
    params: ref({ runJoin: { rx_file: { parse: true } } }),
    deepWatch: true,
    onWatch: (item) => {
      if (item.files) tab.value = 'bulk'
      if (!item.person && person.value._id) {
        const obj = { person: person.value._id };
        const planId = envStore.getPlanId
        if (planId) obj.plan = planId;
        eraserStore.patchInStore(item._id, obj);
        eraserStore.patch(item._id, obj);
      }
    }
  })

  const setId = (val) => {
    if (val) {
      SessionStorage.setItem('rx_session', val);
      setId.value = val;
      const { href } = router.resolve({ ...route, params: { sessionId: val } })
      window.open(href, '_self')
    }
  }

  const med = ref()
  const ndcMed = ref()
  const focused = ref(false);
  const loading = ref(false);

  const prices = ref({ data: [] })
  const close = ref({ data: [] })
  const setNdc = async (v) => {
    ndcMed.value = v;

    loading.value = true;
    try {
      prices.value = await priceStore.find({
        query: {
          $limit: 25,
          $sort: { price: 1 },
          $or: [
            { rxcui: v.rxcui },
            { ndcs: { $in: v.ndcs || [] } },
            { ndc: { $in: v.ndcs || [] } }
          ]
        }
      })
          .catch(() => {
            return prices.value
          })
    } catch (e) {
      console.error(`Error getting prices: ${e.message}`)
    } finally {
      loading.value = false;
    }
    try {
      const query = {
        $limit: 25,
        $sort: { price: 1 },
        price: { $gt: 0 },
        source: { $nin: ['vision', 'bill', 'upload'] },
        _id: { $nin: (prices.value.data || []).map(a => a._id) },
        name: getRegexQuery(v.name.split(' ')[0].toLowerCase())
      }
      console.log('close query', query);
      close.value = await priceStore.find({
        query
      })
          .catch(() => {
            return close.value
          })
    } catch (e) {
      console.error(`Error getting prices: ${e.message}`)
    }

  }

  const inteadOf = {
    message: 'Pharma is complex because prescription meds have a powerful and acute effect on your body\'s chemistry. They are not a balanced treatment plan alone. That\'s also why they are expensive and complicated to sell to the public. Drugs aren\'t sneakers. Distribution is complex. Your doctor can cut right through that for you.',
    items: [['Expensive one-size-fits-all drugs', 'mdi-cash-multiple'], ['Monitoring ❌. Gate-keeping ✅.', 'mdi-slot-machine'], ['Side-effects and bleak health outcomes', 'mdi-coffin']]
  }
  const youGet = {
    message: 'Practicing medicine can include using powerful meds when strong intervention is needed, but there are always trade-offs to manage. That\'s why having your doctor manage your meds actively and directly is the most sensible path. It\'s also why the complexities to sell to a physician are significantly less than to sell retail.',
    items: [['Physician with aligned incentives', 'mdi-stethoscope'], ['Custom plan with ongoing adjustments', 'mdi-home-analytics'], ['Less synthetic drugs - better health', 'mdi-heart-plus']]
  }
</script>

<style lang="scss" scoped>

  .__srch {
    width: 100%;
    max-width: 600px;
    display: grid;
    grid-template-rows: auto auto;
    max-height: 450px;

    > div {
      overflow-y: scroll;
    }
  }

  .__inp {
    background: var(--ir-bg2);
    border-radius: 25px;
    padding: 0 15px;
  }

  .__list {
    width: 100%;
    max-height: 350px;
    overflow-y: scroll;
  }

  .__res {
    margin: 30px 0;
    border-top: 4px solid var(--ir-light);
    border-bottom: 4px solid var(--ir-light);
  }

  .__lists {
    font-size: 1.25rem;
    font-weight: 500;
    padding: 20px min(15px, 1vw);
    border-radius: 12px;
    background: white;
    box-shadow: 2px 2px 6px var(--ir-light);
    margin: 20px 0;

    > div {
      &:first-child {
        text-align: left;
        padding: 5px 10px;
      }
    }

    .__list {

      > div {
        padding: 2px;
        font-weight: 400;
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        font-size: 1.24rem;

        > div {
          padding: 3px;

          &:first-child {
            padding-right: 1rem;
            font-size: 1.5rem;

          }
        }
      }
    }
  }
</style>
