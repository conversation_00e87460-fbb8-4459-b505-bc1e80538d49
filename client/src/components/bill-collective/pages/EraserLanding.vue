<template>
  <q-page class="relative-position">
    <div class="row justify-center _fw t-l-a" v-if="plan?._id || reF?._id">
      <div class="_cent pw2 q-py-md">
        <div class="_fw q-py-md row items-center">
          <div v-if="plan?._id" >
            <div class="flex items-end">
              <default-avatar size-in="30px" v-if="org?.avatar" :model-value="org"></default-avatar>
              <div class="q-px-sm">
                <div class="font-3-4r tw-six text-ir-grey-7">For members of</div>
                <div class="font-7-8r tw-six">{{ plan.name }}</div>
              </div>
            </div>
          </div>
          <div v-if="reF?._id" class="q-mx-sm">
            <div class="flex items-end">
              <default-avatar size-in="30px" :model-value="host?.avatar ? host : reF"></default-avatar>
              <div class="q-px-sm">
                <div class="font-3-4r tw-six text-ir-grey-7">Invited here by</div>
                <div class="font-7-8r tw-six">{{ reF.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


    <router-view></router-view>
  </q-page>
</template>

<script setup>
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';

  import {ref} from 'vue';
  import {usePlans} from 'stores/plans';
  import {useRefs} from 'stores/refs';
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {useOrgs} from 'stores/orgs';
  import {useRoute} from 'vue-router';
  import {useEnvStore} from 'stores/env';

  const planStore = usePlans();
  const refStore = useRefs();
  const orgStore = useOrgs();
  const envStore = useEnvStore();
  const route = useRoute()

  const { item: reF } = idGet({
    store: refStore,
    value: computed(() => LocalStorage.getItem('ref_id'))
  })

  const host = computed(() => reF.value?._fastjoin?.isHost);

  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => envStore.getPlanId || route.query.planId),
    params: ref({ runJoin: { plan_org: true } })
  })

  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => plan.value?._fastjoin?.org || plan.value?.org)
  })

</script>

<style lang="scss" scoped>

</style>
