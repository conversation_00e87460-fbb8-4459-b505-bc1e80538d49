<template>
  <div class="_fw">
    <q-btn class="_inp" flat no-caps @click="dialog = true">
      <span class="q-mr-sm tw-six">Care Need/Event</span>
      <q-icon name="mdi-plus" color="primary"></q-icon>
    </q-btn>
    <div class="_fw q-pt-md">
      <q-input filled placeholder="Search Care Events..." v-model="search.text">
        <template v-slot:prepend>
          <q-icon name="mdi-magnify"></q-icon>
        </template>
      </q-input>
    </div>
    <div class="__cg">
      <div class="__c" v-for="(care, i) in h$.data" :key="`care-${i}`" @click="open(care)">
        <cares-card :model-value="care"></cares-card>
      </div>
    </div>
    <div class="row items-center _fw">
                  <span>{{ ((pagination.currentPage * limit) - limit) + 1 }} - {{ pageRecordCount }} of {{
                      h$.total
                    }}</span>
      <q-space></q-space>
      <q-pagination
          @update:model-value="h$.toPage($event)"
          :model-value="pagination.currentPage"
          :min="1"
          :max="pagination.pageCount"
          direction-links
          boundary-numbers
      ></q-pagination>
    </div>
    <common-dialog setting="right" v-model="dialog">
      <div class="pd2 pw2 bg-white _fw">
        <care-form
            :plan="planId"
        ></care-form>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import CareForm from 'components/care/forms/CareForm.vue';
  import CaresCard from 'components/care/cards/CaresCard.vue';

  import {computed, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useCares} from 'stores/cares';
  import {loginPerson} from 'stores/utils/login';
  import {useRouter} from 'vue-router';
  import {HQuery} from 'src/utils/hQuery';
  import {useEnvStore} from 'stores/env';
  const { person } = loginPerson()

  const envStore = useEnvStore();

  const router = useRouter()

  const store = useCares();

  const props = defineProps({
    plan: { required: false }
  })

  const planId = computed(() => props.plan?._id || envStore.getPlanId)

  const dialog = ref(false);

  const { search, searchQ } = HQuery({})

  const limit = ref(10);
  const { h$, pagination } = HFind({
    store,
    limit,
    params: computed(() => {
      return {
        runJoin: { scrub_empty_care: true },
        query: {
          ...searchQ.value,
          plan: planId.value,
          person: person.value?._id
        }
      }
    })
  })
  const pageRecordCount = computed(() => {
    return Math.min(h$.total, pagination.value.currentPage * limit.value)
  });

  const open = (c) => {
    router.push({ name: 'care-page', params: { careId: c._id } });
  }

</script>

<style lang="scss" scoped>
  .__cg {
    display: grid;
    max-width: 100%;
    overflow-x: scroll;
    grid-template-columns: repeat(auto-fill, minmax(350px, min-content));
    grid-gap: 20px;
    padding: 30px 10px;

    .__c {
      border-radius: 15px;
      background: white;
      box-shadow: 0 2px 10px -4px #999;
      width: 350px;
      max-width: 95vw;
      padding: 20px;
      cursor: pointer;
    }
  }
</style>
