<template>
  <div class="_fw">

    <q-tab-panels class="_panel" animated :model-value="!active">
      <q-tab-panel class="_panel" :name="true">
        <div class="row">
          <div class="col-12 col-md-3 q-pa-xs __dr">
            <q-icon size="15px" name="mdi-filter" color="accent"></q-icon>
            <provider-chip empty-label="Select Provider" clickable @click="providerMenu = true"
                           :model-value="providerFilter">
              <template v-slot:right>
                <q-icon class="q-ml-sm" name="mdi-menu-down"></q-icon>
              </template>
              <template v-slot:menu>
                <common-dialog v-model="providerMenu" setting="smmd">
                  <div class="q-pa-md bg-white _fw">
                    <q-input v-model="pSearch.text" dense filled placeholder="Search Providers...">
                      <template v-slot:prepend>
                        <q-icon name="mdi-magnify"></q-icon>
                      </template>
                    </q-input>
                    <q-list separator>
                      <provider-item
                          simple
                          v-for="(p, i) in p$.data" :key="`p-${i}`"
                          :model-value="p"
                          @update:model-value="providerFilter = p">
                        <template v-slot:side>
                          <q-item-section side v-if="providerFilter?._id === p._id">
                            <q-icon name="mdi-check" color="green"></q-icon>
                          </q-item-section>
                        </template>
                      </provider-item>
                    </q-list>
                  </div>
                </common-dialog>
              </template>
            </provider-chip>
            <q-btn icon="mdi-close" color="red" dense flat size="xs" v-if="providerFilter" @click="providerFilter = undefined"></q-btn>
            <div class="row items-center">
              <div class="col-6 q-pa-xs">
                <inline-date
                    :input-attrs="{ dense: true, filled: true, label: 'After Date' }"
                    v-model="dates.from"
                    clearable
                ></inline-date>
              </div>
              <div class="col-6 q-pa-xs">
                <inline-date
                    :input-attrs="{ dense: true, filled: true, label: 'Before Date' }"
                    v-model="dates.to"
                    clearable
                ></inline-date>
              </div>

            </div>
            <div class="row">
              <div class="col-6 q-pa-xs">
                <money-input dense filled label="Min Balance" :model-value="balance.gte > -1 ? balance.gte : undefined" @update:model-value="balance.gte = $event"></money-input>
              </div>
              <div class="col-6 q-pa-xs">
                <money-input dense filled label="Max Balance" :model-value="balance.lte > -1 ? balance.lte : undefined" @update:model-value="balance.lte = $event"></money-input>
              </div>
            </div>
          </div>
          <div class="col-12 col-md-9">
            <div v-if="!v$.total" class="q-pa-md font-1r text-italic">
              No visits or appointments found
            </div>
            <template v-for="(v, i) in v$.data" :key="`v-${i}`">
              <div class="__c">
                <visit-card
                    @click="$router.push({ ...$route, params: { visitId: v._id }})"
                    :model-value="v">
                  <template v-slot:top-right>
                    <q-space></q-space>
                    <member-chip :model-value="v.patient"></member-chip>
                  </template>
                </visit-card>
              </div>
              <q-separator v-if="i < v$.data.length - 1" color="p3"></q-separator>
            </template>

            <div class="row justify-end q-py-md">
              <q-pagination
                  @update:model-value="v$.toPage($event)"
                  :model-value="pagination.currentPage"
                  :min="1"
                  :max="pagination.pageCount"
                  direction-links
                  boundary-numbers
              ></q-pagination>
            </div>
          </div>
        </div>

      </q-tab-panel>
      <q-tab-panel class="_panel" :name="false">
        <visit-page context="admin"></visit-page>
      </q-tab-panel>

    </q-tab-panels>

  </div>
</template>

<script setup>
  import VisitCard from 'components/care/visits/cards/VisitCard.vue';
  import MemberChip from 'components/households/cards/MemberChip.vue';
  import VisitPage from 'components/care/visits/pages/VisitPage.vue';
  import InlineDate from 'components/common/dates/InlineDate.vue';
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import ProviderItem from 'components/providers/cards/ProviderItem.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';

  import {HFind} from 'src/utils/hFind';
  import {idGet} from 'src/utils/id-get';
  import {computed, onMounted, ref, watch} from 'vue';
  import {useVisits} from 'stores/visits';
  import {usePlans} from 'stores/plans';
  import {useRoute} from 'vue-router';
  import {useProviders} from 'stores/providers';
  import {HQuery} from 'src/utils/hQuery';
  import {useCrossSections} from 'stores/cross-sections';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const envStore = useEnvStore();
  const { getPlanId } = contextItems(envStore);

  const visitStore = useVisits();
  const planStore = usePlans();
  const providerStore = useProviders();
  const csStore = useCrossSections();
  const route = useRoute();

  const crossSection = ref({})

  const { item: plan } = idGet({
    store: planStore,
    value: getPlanId,
    onWatch: async (item) => {
      if(!crossSection.value?._id){
        const cs = await csStore.find({ query: { $limit: 1, hackId: `plan_providers:plans:${item._id}`}})
        if(cs) crossSection.value = cs.data[0]
      }
    }
  })

  const { item: active } = idGet({
    store: visitStore,
    routeParamsPath: 'visitId'
  })

  const providerMenu = ref(false)

  const dates = ref({
    from: undefined,
    to: undefined
  });
  const sort = ref({ date: 1 })
  const balance = ref({
    gte: -1,
    lte: -1
  });
  const providerFilter = ref(undefined)
  const params = computed(() => {
    const query = { plan: plan.value?._id, $sort: sort }
    if (dates.value.from) query.date = { $gte: new Date(dates.value.from) }
    if (dates.value.to) query.date = { ...query.date, $lte: new Date(dates.value.to) }
    if (balance.value.gte > -1) query.balance = { $gte: balance.value.gte }
    if (balance.value.lte > -1) query.balance = { ...query.balance, $lte: balance.value.lte }
    if (providerFilter.value) query.provider = providerFilter.value._id
    return { query };
  })

  const { h$: v$, pagination } = HFind({
    store: visitStore,
    limit: ref(10),
    params
  })
 watch(params, (nv) => {
    if (nv) {
      const toQueryString = (queryObject) => {
        const url = new URL(window.location);
        for (const key in queryObject) {
          if (queryObject.hasOwnProperty(key)) {
            const value = queryObject[key];
            if (Array.isArray(value)) {
              // If the value is an array, add each item individually
              value.forEach(item => url.searchParams.set(key, item));
            } else if (value !== undefined && value !== null) {
              // Only add if the value is not undefined or null
              url.searchParams.set(key, value);
            }
          }
        }
        return url.toString();
      }
      const str = toQueryString(nv.query || {});
      window.history.replaceState(null, '', str); // Update the browser's URL without reloading the page
    }
  }, {immediate: true})

  const { search: pSearch, searchQ } = HQuery({})

  const { h$: p$ } = HFind({
    store: providerStore,
    params: computed(() => {
      return {
        query: {
          _id: { $in: crossSection.value?.sections?.providers },
          ...searchQ.value,
        }
      }
    })
  })

  const fromQueryString = (queryString) => {
    const params = new URLSearchParams(queryString);
    const queryObject = {};

    for (const [key, value] of params.entries()) {
      // Check if the key already exists in the object
      if (queryObject.hasOwnProperty(key)) {
        // If it's already an array, push the new value
        if (Array.isArray(queryObject[key])) {
          queryObject[key].push(value);
        } else {
          // If it's not an array, convert it into an array
          queryObject[key] = [queryObject[key], value];
        }
      } else {
        // Add the key-value pair to the object
        queryObject[key] = value;
      }
    }

    return queryObject;
  }
  const resetQuery = async (query) => {
    const obj = query;
    const arr = Object.keys(obj);
    for (let i = 0; i < arr.length; i++) {
      const key = arr[i]
      if (key === 'date') {
        dates.value.from = JSON.parse(obj.date).$gte
        dates.value.to = JSON.parse(obj.date).$lte
      }
      if (key === 'balance') {
        balance.value.gte = JSON.parse(obj.balance).$gte;
        balance.value.let = JSON.parse(obj.balance).$lte;
      }
      if (key === 'provider') {
        const id = obj.provider
        if (id) {
          let p = providerStore.getFromStore(id)?.value;
          if (!p) p = await providerStore.get(id);
          providerFilter.value = p;
        }
      }
    }
  }
  onMounted(async () => {
    resetQuery(route.query);
  })

</script>

<style lang="scss" scoped>
  .__c {
    border-radius: 12px;
    box-shadow: none;
    padding: 25px 2vw;
    margin: 15px 0;
    background: white;
    cursor: pointer;
    transition: all .4s ease;
    transform: none;

    &:hover {
      //background: linear-gradient(100deg, var(--q-p0), white);
      box-shadow: 2px 2px 12px -6px #999;
      transform: translate(0, -2px);
    }
  }

  .__dr {
    border-right: solid 1px #dedede;
  }

  @media screen and (max-width: 1023px) {
    .__dr {
      border-right: none;
    }
  }
</style>
