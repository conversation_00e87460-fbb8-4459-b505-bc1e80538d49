<template>
  <div class="_fw">

    <div class="_fw pd1 pw1">
      <div class="row">
        <div class="__t">Unassigned Care Events <q-btn dense flat icon="mdi-open-in-new" @click="openCare"></q-btn></div>
      </div>
      <div class="__r">
        <div v-for="(care, i) in e$.data" :key="`care-${i}`" class="__c">
          <div class="_fw __in" @click="$router.push({ name: 'plan-cares', params: { careId: care._id }})">
            <cares-card :model-value="care"></cares-card>
          </div>
        </div>
      </div>
    </div>
    <div class="_fw pd1 pw1">
      <div class="row">
        <div class="__t">Visits w/ Balance
          <q-btn dense flat icon="mdi-open-in-new" @click="openBalanceVisit()"></q-btn>
        </div>
      </div>
      <div class="__r">
        <div v-for="(visit, i) in v$.data" :key="`visit-${i}`" class="__c">
          <div class="_fw __in" @click="$router.push({ name: 'plan-visits', params: { visitId: visit._id }})">
            <div class="row">
              <q-chip color="transparent">
                <q-avatar size="14px" :color="visitType(visit).color || 'grey-5'">
                  <q-tooltip>{{ visitType(visit).label || 'No Status' }}</q-tooltip>
                </q-avatar>
                <span
                    class="q-ml-sm font-7-8r">{{
                    formatDate(visit?.date, 'ddd MMM DD, YYYY')
                  }}{{ visit?.endDate ? `- ${formatDate(visit?.endDate, 'ddd MMM dd, YYYY')}` : '' }}</span>
              </q-chip>
            </div>
            <provider-chip :model-value="visit.provider"></provider-chip>
            <member-chip :model-value="visit.patient"></member-chip>
            <div class="row q-pa-sm">
              <div class="font-1r tw-six text-primary">
                <span class="font-3-4r text-grey-10">Balance:&nbsp;</span>
                {{ dollarString((visit.balance || 0) / 100, '$', 0) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="_fw pd1 pw1">
      <div class="row">
        <div class="__t">Appointments Upcoming
          <q-btn dense flat icon="mdi-open-in-new" @click="openAppointement()"></q-btn>
        </div>
      </div>
      <div class="__r">
        <div v-if="!a$.total" class="__c">
          <div class="__in _fw">
            <div class="q-pa-md font-1r text-italic">None Found</div>
          </div>
        </div>
        <div v-for="(visit, i) in a$.data" :key="`appt-${i}`" class="__c">
          <div class="_fw __in" @click="$router.push({ name: 'plan-visits', params: { visitId: visit._id }})">
            <div class="row">
              <q-chip color="transparent">
                <q-avatar size="14px" :color="visitType(visit).color || 'grey-5'">
                  <q-tooltip>{{ visitType(visit).label || 'No Status' }}</q-tooltip>
                </q-avatar>
                <span
                    class="q-ml-sm font-7-8r">{{
                    formatDate(visit?.date, 'ddd MMM DD, YYYY')
                  }}{{ visit?.endDate ? `- ${formatDate(visit?.endDate, 'ddd MMM dd, YYYY')}` : '' }}</span>
              </q-chip>
            </div>
            <provider-chip :model-value="visit.provider"></provider-chip>
            <member-chip :model-value="visit.patient"></member-chip>
            <div class="row q-pa-sm">
              <div class="font-1r tw-six text-primary">
                <span class="font-3-4r text-grey-10">Balance:&nbsp;</span>
                {{ dollarString((visit.balance || 0) / 100, '$', 0) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import CaresCard from 'components/care/cards/CaresCard.vue';
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import MemberChip from 'components/households/cards/MemberChip.vue';

  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {computed} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useCares} from 'stores/cares';
  import {useVisits} from 'stores/visits';
  import {formatDate} from 'src/utils/date-utils';
  import {visitType} from 'components/care/visits/utils';
  import {dollarString} from 'src/utils/global-methods';
  import {useRouter} from 'vue-router';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const planStore = usePlans();
  const caresStore = useCares()
  const visitStore = useVisits();
  const envStore = useEnvStore();
  const { getPlanId } = contextItems(envStore);

  const router = useRouter();

  const { item: plan } = idGet({
    store: planStore,
    value: getPlanId
  })

  const { h$: e$ } = HFind({
    store: caresStore,
    params: computed(() => {
      return {
        query: {
          patient: { $exists: true },
          plan: plan.value?._id,
          'visits.0': { $exists: false },
          $sort: { patientPriority: 1 }
        }
      }
    })
  })

  const { h$: v$ } = HFind({
    store: visitStore,
    params: computed(() => {
      return {
        query: {
          plan: plan.value._id,
          balance: { $gte: 0 },
          $sort: { balance: -1 }
        }
      }
    })
  })

  const { h$: a$ } = HFind({
    store: visitStore,
    params: computed(() => {
      return {
        query: {
          date: { $gte: new Date() },
          $sort: { date: 1 },
        }
      }
    })
  })

  const openBalanceVisit = () => {
    router.push({ name: 'plan-visits', query: { balance: JSON.stringify({ $gte: 0 }) }})
  }
  const openAppointement = () => {
    router.push({
      name: 'plan-visits',
      query: { date: JSON.stringify({ $gte: new Date() })}
    })
  }
  const openCare = () => {
    router.push({
      name: 'plan-cares',
      query: {
        'visits.0': JSON.stringify({ $exists: false })
      }
    })
  }

</script>

<style lang="scss" scoped>
  .__t {
    font-weight: 600;
    font-size: 1rem;
    background: linear-gradient(12deg, var(--q-p6), var(--q-primary));
    padding: 10px 20px;
    border-radius: 5px 5px 0 0;
    color: white;
    transform: translate(0, 2px);
    //margin-left: 10px;
  }

  .__r {
    display: flex;
    width: 100%;
    overflow-x: scroll;
    padding: 10px;
    box-shadow: 0 2px 8px -3px #999;
    border-radius: 0 10px 10px 10px;
    background: white;

    .__c {
      padding: 5px 10px 5px 0px;

      .__in {
        cursor: pointer;
        border: solid 2px var(--q-primary);
        width: 330px;
        max-width: 90vw;
        border-radius: 12px;
        background: white;
        //box-shadow: 0 2px 4px rgba(0,0,0,.2);
        padding: 20px 15px;
        flex-shrink: 0;
      }
    }
  }
</style>
