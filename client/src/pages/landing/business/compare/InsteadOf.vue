<template>
  <div class="__io row">
    <div class="col-12 col-md-6 col-lg-5">
      <div class="_fw pw1 q-py-md">
        <div class="__pre">Instead of:</div>
        <div class="__title">One-Size-Fits-All</div>
        <div class="__box">

          <div class="row">
            <div>
              <template v-for="(hh, i) in hhs" :key="`hh-${i}`">
                <div class="flex items-center text-ir-deep">

                  <div class="tw-six">{{ hh.name }}</div>
                  <family-member-icon :model-value="hh.mbr">
                    <q-tooltip class="bg-ir-deep text-xs tw-six">{{ hh.mbr.age }}</q-tooltip>
                  </family-member-icon>
                  <div>{{ hh.mbr.age }}</div>
                  <q-icon v-if="hh.mbrs" name="mdi-plus-thick" color="ir-mid" size="12px" class="q-ml-sm"></q-icon>
                  <family-member-icon v-for="(mbr, idx) in hh.mbrs" :key="`mbr-${i}-${idx}`"
                                      :model-value="mbr">
                    <q-tooltip class="bg-ir-deep text-xs tw-six">{{ mbr.age }}</q-tooltip>
                  </family-member-icon>

                </div>
                <q-separator v-if="i < hhs.length - 1" class="q-my-sm"></q-separator>
              </template>
            </div>
          </div>
        </div>

        <div class="__box">

          <div class="_fw"
               v-for="(item, idx) in ['Same plan - same benefits', 'Direct shared risk & cost', 'Chosen by management', 'Use it or forfeit the pay']"
               :key="`reason-${idx}`">
            <div class="font-1r">
              <span class="text-ir-deep tw-six">{{ item }}</span>
              <q-icon size="17px" class="q-ml-sm" color="secondary" name="mdi-check-circle"></q-icon>
            </div>
            <q-separator class="q-my-sm" v-if="idx < 3"></q-separator>
          </div>

        </div>

      </div>
      <q-separator :vertical="$q.screen.gt.sm"></q-separator>
    </div>
    <div class="col-12 col-md-6 col-lg-5">
      <div class="_fw pw1 q-py-md">
        <div class="__pre">Move to:</div>
        <div class="__title">Employee-Directed</div>

        <div class="__box">
          <div class="row">
            <div>
              <q-tab-panels class="_panel" :model-value="idxs.mbr" animated transition-prev="slide-down"
                            transition-next="slide-up">
                <q-tab-panel class="_panel" v-for="(hh, i) in hhs" :key="`hhb-${i}`" :name="i">
                  <div class="flex items-center font-1-1-4r text-ir-deep">
                    <div class="text-center tw-six">{{ hh.name }}</div>
                    <family-member-icon :model-value="hh.mbr"></family-member-icon>
                    <div>{{ hh.mbr.age }}</div>
                    <q-icon v-if="hh.mbrs" name="mdi-plus-thick" color="ir-mid" size="12px" class="q-ml-sm"></q-icon>
                    <family-member-icon v-for="(mbr, idx) in hh.mbrs || []" :key="`mbr-${i}-${idx}`"
                                        :model-value="mbr">
                      <q-tooltip class="bg-ir-deep text-xs tw-six">{{ mbr.age }}</q-tooltip>
                    </family-member-icon>
                  </div>
                </q-tab-panel>
              </q-tab-panels>


              <q-separator class="q-my-sm"></q-separator>
            </div>
          </div>
          <div class="font-1-1-8r alt-font tw-five">{{ hhs[idxs.mbr].choice }}</div>
        </div>
        <div class="__box">
          <div class="row q-pb-md">
            <ai-logo></ai-logo>
          </div>
          <div class="row">
            <div class="_oh">
              <div v-for="(opt, i) in hhs[idxs.mbr].opts" :key="`opt-${i}`"
                   :class="`__opt ${idxs.opt >= i ? '' : '__off'}`">
                <div>
                  <span class="text-ir-deep tw-six q-mr-sm font-1r">{{ opt.label }}</span>
                  <q-icon size="17px" :color="idxs.on.includes(i) ? 'primary' : 'ir-light'" class="__icon"
                          name="mdi-check-circle"></q-icon>
                  <q-separator class="q-my-sm" v-if="i < hhs[idxs.mbr].opts.length - 1"></q-separator>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row justify-center items-center q-pt-md">
          <div class="flex items-center" v-for="(hh, i) in hhs" :key="`cl-${i}`">
            <q-chip dense square color="transparent" clickable @click="setMbr(i); pause = true" :label="hh.name"
                    :class="idxs.mbr === i ? 'text-ir-deep tw-six' : 'text-ir-deep'"></q-chip>
            <div class="q-px-sm" v-if="i < hhs.length - 1">|</div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script setup>
  import FamilyMemberIcon from 'components/households/utils/FamilyMemberIcon.vue';
  import {onMounted, ref} from 'vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';

  const hhs = [
    {
      name: 'Alice',
      mbr: { gender: 'F', age: 55 },
      mbrs: [
        { gender: 'M', relation: 'spouse', age: 58 },
      ],
      choice: 'Alice\'s husband has tri-care, so she signs up for DPC to help manage her diabetes, and puts the rest in her HSA',
      idx: [1, 2],
      opts: [
        { label: 'Blue Cross Blue Shield PPO 1000/15' },
        {
          label: 'Health Savings Account'
        },
        {
          label: 'Direct Primary Care Plan'
        }
      ]
    },
    {
      name: 'Esais',
      mbr: { gender: 'M', age: 32 },
      mbrs: [
        { gender: 'F', relation: 'spouse', age: 32 },
        { gender: 'M', relation: 'child', age: 12 },
        { gender: 'F', relation: 'child', age: 7 },
      ],
      choice: 'Esais\' family is welcoming a new baby in a few months - so he chooses a plan that will cover maternity care thoroughly',
      idx: [0, 2],
      opts: [
        { label: 'Kaiser HMO Gold 80' },
        { label: 'Medishare $3,000 IUA Plan' },
        { label: 'Pocket Guard Gap Plan' }
      ]
    },
    {
      name: 'Rudy',
      mbr: { gender: 'M', age: 26 },
      choice: 'Rudy doesn\'t need much healthcare. A medical cost share gives him great "just in case" coverage for a painless $102/month',
      idx: [1, 2],
      opts: [{ label: 'Aetna HDHP Silver' }, { label: 'Sedera $2,500 IUA Plan' }, { label: 'Heath Savings Plan' }]
    }
  ]

  const idxs = ref({
    mbr: 0,
    opt: -1,
    on: []
  })

  const pause = ref(false);

  const setMbr = (idx) => {
    idxs.value.on = [];
    idxs.value.opt = -1;
    idxs.value.mbr = idx;
    const setOpt = () => {
      setTimeout(() => {
        if (idxs.value.opt < 3) {
          idxs.value.opt++
          setOpt()
        } else {
          idxs.value.on = hhs[idxs.value.mbr].idx
          setTimeout(() => {
            if (!pause.value) setMbr(idx < hhs.length - 1 ? idx + 1 : 0);
          }, 6000)
        }
      }, 1000)
    }
    setOpt()
  }
  const runRotation = () => {
    if (idxs.value.mbr < hhs.length - 1) setMbr(idxs.value.mbr + 1);
    else setMbr(0);
  }

  onMounted(() => {
    runRotation()
  })
</script>

<style lang="scss">

  .__io {

    > div {
      padding: 30px max(15px, 1vw);
      border-radius: 15px;
      //background: rgba(240,240,240,.6);
      border: solid 3px var(--ir-light);
      margin: 10px;
      }
    }


  .__box {
    padding: 15px 0;
    //border-radius: 20px;
    //background: var(--ir-bg2);
    //box-shadow: 0 2px 8px var(--ir-light);
  }


  .__icon {
    transition: all .3s;
  }

  .__opt {
    transition: all .3s;
    transform: none;
  }

  .__off {
    //opacity: 0;
    transform: translate(400%, 0);
  }

  .__pre {
    font-size: 1rem;
    font-weight: 500;
  }
  .__title {
    font-size: 1.5rem;
    font-weight: 600;
    padding-bottom: 15px;
  }
</style>
