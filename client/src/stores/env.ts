import {defineStore} from 'pinia';
import {IpInfo} from 'src/fingerprints/fingerprintjs';
import {LocalStorage, SessionStorage} from 'symbol-auth-client';
import {Address, miToKm} from 'src/utils/geo-utils';
import {_get} from 'symbol-syntax-utils';
import type {AnyObj} from 'src/utils/types';
import {getStateCode} from 'components/common/geo/data/states';

export type Modes = 'consumer' | 'sponsor' | 'provider';

const checkGender = (v: string, def?: string) => {
    if (typeof v !== 'string') return def || 'male'
    if (v.toLowerCase().includes('f')) return 'female'
    return 'male'
}
const getAge = (v: any) => {
    const num = Number(v)
    if (!isNaN(num)) return num;
}
const setP = (v: any) => {
    if (!Array.isArray(v)) return undefined
    return [getAge(v[0]), checkGender(v[1])]
}

type Location = {
    city?: string,
    county?: string,
    lngLat?: [number, number],
    region?: string,
    country?: string
    postal?: string,
    fips?: string
};

type Gender = 'male' | 'female'
type StateObj = {
    orgId?: string,
    planId?: string,
    hostId?: string,
    refId?: string,
    chatWindow: number,
    isDark: boolean,
    mode: Modes,
    age: number,
    gender: Gender,
    income: number,
    spouse?: [number, Gender],
    deps: Array<[number, Gender]>,
    modeUpdated: boolean,
    mobile: boolean,
    ipLoading: boolean,
    location?: Location,
    address?: Partial<Address> & { [key: string]: any }
    ipInfo: IpInfo,
    geo_range?: number,
    settings: AnyObj,
    place?: { countyfips: string, zipcode: string, state: string }
    profileBadges: { [key: string]: { name: string, emoji?: string } }
}
export const useEnvStore = defineStore('env', {
    state: (): StateObj => ({
        orgId: undefined,
        planId: undefined,
        refId: undefined,
        hostId: undefined,
        mobile: false,
        chatWindow: 0,
        isDark: false,
        mode: 'consumer',
        modeUpdated: false,
        ipLoading: false,
        ipInfo: {},
        place: undefined,
        income: 85000,
        location: undefined,
        address: undefined,
        settings: {},
        geo_range: 29,
        age: 40,
        gender: 'male',
        deps: [],
        spouse: undefined,
        profileBadges: {
            'group_admin': {name: 'Group Admin', emoji: '📛'},
            'participant': {name: 'Group Participant', emoji: '❤️'},
            'business_owner': {name: 'Business Owner', emoji: '👔'},
            'md': {name: 'MD', emoji: '🥼'},
            'provider': {name: 'Medical Provider', emoji: '⚕️'},
            'nurse': {name: 'Nurse', emoji: '🧑‍⚕️'},
            'pa': {name: 'PA', emoji: '🩺'},
            'np': {name: 'Nurse Practitioner', emoji: '🏥'},
            'medical_admin': {name: 'Medical Admin', emoji: '📋'}
        }
    }),
    getters: {
        dark: (state) => {
            return state.isDark;
        },
        getIp: (state) => {
            return state.ipInfo?.ip
        },
        getEnv: (state) => {
            return (path: string | Array<string>, def?: AnyObj) => {
                if (path) {
                    const p = Array.isArray(path) ? path : [path];
                    return _get(state, ['settings', ...p], def);
                } else return state.settings ? state.settings : def;
            };
        },
        city(state) {
            return state.location?.city || state.ipInfo?.city
        },
        county(state) {
            return state.location?.county
        },
        region(state) {
            return state.location?.region || state.ipInfo?.region
        },
        country(state) {
            return state.location?.country || state.ipInfo?.country
        },
        lngLat(state) {
            return state.location?.lngLat || state.ipInfo.lngLat
        },
        getOrgId(state) {
            return state.orgId || LocalStorage.getItem('org_id');
        },
        getPlanId(state) {
            return state.planId || LocalStorage.getItem('plan_id');
        },
        getHostId(state) {
            return state.hostId || LocalStorage.getItem('host_id');
        },
        getRefId(state) {
            return state.refId || LocalStorage.getItem('ref_id');
        },
        household(state) {
            const people = [{age: state.age, gender: state.gender, relation: 'self'}]
            if ((state.spouse || [])[0]) people.push({age: state.spouse[0], gender: state.spouse[1], relation: 'spouse'})
            for (const dep of state.deps || []) {
                people.push({age: dep[0], gender: dep[1], relation: 'child'})
            }
            return {
                people,
                income: state.income,
                place: state.place
            }
        }
    },
    actions: {
        setMobile(v){
            this.mobile = !!v;
        },
        setIncome(v) {
            const num = Number(v)
            if (!isNaN(num)) {
                this.income = num
                LocalStorage.setItem('income', this.income)
            }
        },
        setAge(v) {
            this.age = getAge(v);
            LocalStorage.setItem('age', this.age);
        },
        setGender(v) {
            this.gender = checkGender(v, this.gender)
            LocalStorage.setItem('gender', this.gender)
        },
        setDeps(v) {
            if (!Array.isArray(v)) return;
            this.deps = v.map(a => setP(a)).filter(a => !!a)
            LocalStorage.setItem('deps', this.deps)
        },
        setSpouse(v) {
            const val = setP(v)
            this.spouse = val;
            if (val) {
                LocalStorage.setItem('spouse', val)
            } else LocalStorage.removeItem('spouse')
        },
        setOrgId(v) {
            this.orgId = v;
            if (v) LocalStorage.setItem('org_id', v);
            else LocalStorage.removeItem('org_id')
        },
        setPlanId(v) {
            // console.log('set plan id', v, source);
            this.planId = v;
            if (v) LocalStorage.setItem('plan_id', v);
            else LocalStorage.removeItem('plan_id');
        },
        setHostId(v) {
            this.orgId = v;
            if (v) LocalStorage.setItem('host_id', v);
            else LocalStorage.removeItem('host_id')
        },
        setRefId(v) {
            this.orgId = v;
            if (v) LocalStorage.setItem('ref_id', v);
            else LocalStorage.removeItem('ref_id')
        },
        setChatWindow(v) {
            this.chatWindow = !!v;
        },
        setDark(v, set) {
            if (set) set(!!v)
            this.isDark = !!v;
            SessionStorage.setItem('dark', !!v);
        },
        ipIsLoading(bool: boolean) {
            this.ipLoading = bool;
        },
        setGeoRange(num?: number) {
            if (!num) num = Number(SessionStorage.getItem('geo_range') || miToKm(20));
            if (num) {
                SessionStorage.setItem('geo_range', num);
                this.geo_range = num;
            }
        },
        setIp(ipInfo: IpInfo) {
            this.ipInfo = ipInfo;
        },
        setPlace(place: { zipcode: string, state: string, countyfips: string }) {
            if (place?.countyfips) {
                try {
                    this.place = place;
                    const map = {'zipcode': 'postal', 'state': 'region', 'countyfips': 'fips'}
                    if (!this.location) this.location = {}
                    for (const k in place) {
                        if (place[k] && this.location[map[k]] !== place[k]) {
                            this.location[map[k]] = place[k]
                            this.address[map[k]] = place[k]
                        }
                    }
                } catch (e) {
                    console.log('error setting place', e.message);
                }
            }
        },
        setCity(city) {
            if (city) {
                this.address = {...this.address, city}
                this.location = {...this.location, city}
            }
        },
        setAddress(val: Partial<Address>) {
            if (val) {
                const location: Location = {
                    city: val.city,
                    country: val.country,
                    region: val.region,
                    postal: val.postal
                };
                if (val.longitude && val.latitude) location.lngLat = [val.longitude, val.latitude];
                this.location = location;
                this.address = val;
            } else SessionStorage.removeItem('location')
        },
        setLocation(val: Location) {
            if (val) {
                const location: any = {};
                for (const k of ['country', 'region', 'county', 'fips', 'city', 'postal']) {
                    if (val[k]) location[k] = val[k]
                }
                this.location = location;
                SessionStorage.setItem('location', location);

                let resetAddress = false;
                const newAddress = {};
                const adr = this.address || {}
                for (const key of ['city', 'country', 'region', 'postal']) {
                    if (location[key] && location[key] !== adr[key]) {
                        resetAddress = true;
                        newAddress[key] = location[key];
                    }
                }
                if (location.fips) this.place = {
                    countyfips: location.fips,
                    zipcode: location.postal,
                    state: getStateCode(location.region)
                }
                if (resetAddress) this.address = newAddress
            }
        },
        setMode(val: Modes) {
            if (['consumer', 'sponsor', 'provider'].includes(val)) {
                this.mode = val;
                this.modeUpdated = true;
            }
        },
    },
});
