import {commonRoutes} from '../utils/common-routes';

export const providerRoutes = (orgId: any, providerId: any) => {
    return [
        {
            path: '/',
            component: () => import('src/layouts/ProviderLayout.vue'),
            children: [
                ...commonRoutes(),
                ...[
                    {
                        path: '',
                        name: 'provider-dash',
                        component: () => import('src/components/providers/pages/ProviderDash.vue')
                    },
                    {
                        path: '/care',
                        meta: {
                            category: 'care'
                        },
                        component: () => import('src/components/providers/pages/ProviderCare.vue'),
                        children: [
                            {
                                path: '',
                                name: 'provider-care',
                                meta: {
                                    name: 'events'
                                },
                                component: () => import('src/components/providers/pages/ProviderCareEvents.vue'),
                            },
                            {
                                path: 'event/:careId',
                                name: 'provider-care-event',
                                meta: {name: 'events'},
                                props: {providerView: true},
                                component: () => import('src/components/care/pages/CareDetail.vue')
                            }
                        ]
                    },
                    {
                        path: 'visits',
                        name: 'provider-visits',
                        meta: {name: 'visits', category: 'care'},
                        component: () => import('src/components/providers/pages/ProviderVisits.vue')
                    },
                    {
                        path: '/visit/:visitId/:claimId?',
                        name: 'visit-page',
                        component: () => import('src/components/care/visits/pages/ProviderVisitPage.vue'),
                        meta: {
                            category: 'care',
                            name: 'visits'
                        }
                    },
                    {
                        path: '/claims',
                        name: 'claims-page',
                        component: () => import('src/components/providers/pages/ProviderClaims.vue'),
                        meta: {
                            category: 'care',
                            name: 'claims',
                            sub: 'claims'
                        }
                    },
                    {
                        path: '/finance',
                        meta: {category: 'finance'},
                        component: () => import('src/components/accounts/pages/OrgBanking.vue'),
                    },
                    {
                        path: '/account',
                        meta: {category: 'provider'},
                        component: () => import('src/components/providers/pages/ProviderProfile.vue'),
                        children: [
                            {
                                path: '/add',
                                name: 'add-provider',
                                component: () => import('src/components/providers/pages/CreateProvider.vue')
                            },
                            {
                                path: '',
                                name: 'provider-profile',
                                meta: {name: 'profile'},
                                component: () => import('src/components/providers/pages/FullProfile.vue')
                            },
                            {
                                path: '/video',
                                name: 'provider-video',
                                meta: {name: 'video'},
                                component: () => import('src/components/providers/videos/pages/ProviderVideos.vue')
                            }
                        ].map((a: any) => {
                            return {
                                ...a,
                                meta: {...a.meta, category: 'provider'}
                            }
                        })
                    },
                    {
                        path: 'bundle/:bookId?',
                        name: 'provider-bundle',
                        meta: {name: 'bundle', category: 'bundle'},
                        component: () => import('src/components/providers/bundles/pages/ProviderPrices.vue')
                    },
                    {
                        path: 'direct',
                        meta: { name: 'direct-care', category: 'direct-care'},
                        component: () => import('src/components/providers/direct-care/DirectCareHome.vue'),
                        children: [
                            {
                                path: '',
                                name: 'direct-care-home',
                                meta: { name: 'direct-care', category: 'direct-care'},
                                component: () => import('src/components/providers/direct-care/DirectCareDash.vue'),
                            },
                            {
                                path: 'memberships',
                                name: 'dc-coverages',
                                meta: { category:'direct-care', name: 'coverages'},
                                component: () => import('src/components/providers/direct-care/coverages/DCCoverages.vue'),
                            }
                        ]
                    },
                    {
                        path: 'payments',
                        meta: {
                            category: 'payments',
                            name: 'provider-payments'
                        },
                        component: () => import('src/components/providers/finance/pages/ProviderFinance.vue'),
                        children: [
                            {
                                path: '',
                                name: 'care-account-dash',
                                meta: {sub: 'dash'},
                                component: () => import('src/components/care-accounts/pages/CareAccount.vue')
                            },
                            {
                                path: 'account-settings',
                                name: 'care-account-settings',
                                meta: {sub: 'settings'},
                                component: () => import('src/components/care-accounts/pages/CareAccountSettings.vue')
                            }
                        ]
                        //     .map(a => {
                        //     return {
                        //         ...a,
                        //         meta: {
                        //             category: 'finance',
                        //             name: 'care-account',
                        //             ...a.meta
                        //         }
                        //     }
                        // })
                    },
                    {
                        path: 'payments',
                        name: 'provider-payments',
                        meta: {name: 'payments'},
                        component: () => import('src/components/providers/payments/pages/ProviderPayments.vue')
                    },
                    {
                        path: '/settings',
                        meta: {
                            category: 'settings',
                            ucan: {
                                requiredCapabilities: [[`orgs:${orgId}`, ['orgAdmin']], [`orgs:${orgId}`, ['WRITE']], ['orgs', 'WRITE']],
                                or: true,
                            }
                        },
                        props: {
                            modelValue: orgId
                        },
                        component: () => import('src/components/orgs/pages/OrgDetails.vue'),
                        children: [
                            {
                                path: '',
                                name: 'org-settings',
                                meta: {name: 'org-info'},
                                component: () => import('src/components/orgs/forms/OrgInfo.vue'),
                                props: {
                                    modelValue: orgId
                                }
                            },
                            {
                                path: '/control',
                                name: 'org-control',
                                meta: {name: 'org-control'},

                                component: () => import('src/components/orgs/control/OrgControl.vue'),
                                props: {
                                    modelValue: orgId
                                }
                            },
                            {
                                path: '/ownership',
                                name: 'org-ownership',
                                meta: {name: 'org-ownership'},

                                component: () => import('src/components/orgs/owners/OrgOwnership.vue'),
                                props: {
                                    modelValue: orgId
                                }
                            },
                            {
                                path: '/info',
                                name: 'org-info',
                                meta: {name: 'org-info'},

                                component: () => import('src/components/orgs/forms/OrgInfo.vue'),
                                props: {
                                    modelValue: orgId
                                }
                            },
                            {
                                path: '/banking',
                                meta: { name: 'org-banking'},
                                children: [
                                    {
                                        path: '',
                                        name: 'org-banking',
                                        meta: {
                                            sub: 'bank-accounts'
                                        },
                                        component: () => import('src/components/accounts/forms/OrgAccounts.vue')
                                    },
                                    {
                                        path: 'profile',
                                        name: 'banking-profile',
                                        meta: {sub: 'banking-profile'},
                                        component: () => import('src/components/accounts/treasury/pages/ConnectAccount.vue')
                                    },
                                        //     .map(a => {
                                        //     return {
                                        //         ...a,
                                        //         meta: {category: 'finance', name: 'finance-settings', ...a.meta}
                                        //     }
                                        // })

                                ].map(a => {
                                    return {
                                        ...a,
                                        meta: {
                                            category: 'settings',
                                            name: 'provider-banking',
                                            ...a.meta,
                                        }
                                    }
                                })
                            }
                        ]
                    },
                ].map((a: any) => {
                    return {
                        ...a,
                        meta: {
                            ucan: !providerId ? true : {
                                requiredCapabilities: [[`providers:${providerId}`, ['providerAdmin']], [`providerId:${providerId}`, ['WRITE']], ['providers', 'WRITE']],
                                or: true,
                                cap_subjects: [providerId],
                                log: false
                            },
                            ...a.meta,
                        }
                    }
                })
            ]
        }

    ]
}
