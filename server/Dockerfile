## Creating Node enviorment
FROM node:slim
RUN rm /bin/sh && ln -s /bin/bash /bin/sh
RUN apt-get update >/dev/null \
    && apt-get install -y curl >/dev/null \
    && apt-get -y autoclean >/dev/null
ENV NVM_DIR /usr/local/nvm
RUN mkdir -p /usr/local/nvm
COPY .nvmrc .
COPY .testfile .

ARG NODE_VERSION
ENV NODE_VERSION=18.12.0

RUN curl https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.1/install.sh | bash
RUN source $NVM_DIR/nvm.sh \
    && nvm install $NODE_VERSION \
    && nvm alias default $NODE_VERSION \
    && nvm use default 
    
ENV NODE_PATH $NVM_DIR/v$NODE_VERSION/lib/node_modules
ENV PATH $NVM_DIR/versions/node/v$NODE_VERSION/bin:$PATH
# ENV NODE_PATH $NVM_DIR/v$NODE_VERSION/lib/node_modules
# ENV PATH $NVM_DIR/v$NODE_VERSION/bin:$PATH

RUN node -v
RUN npm -v

# Mode package.json into a tmp directory
ADD package.json /tmp/package.json

ADD ./ /commoncare/server/

WORKDIR /commoncare/server

# Install the dependancies
RUN npm install -q

# RUN npm run compile
# Run the application
CMD ["npm", "run", "dev"]