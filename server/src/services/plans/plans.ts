// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    plansDataValidator,
    plansPatchValidator,
    plansQueryValidator,
    plansResolver,
    plansExternalResolver,
    plansDataResolver,
    plansPatchResolver,
    plansQueryResolver
} from './plans.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {PlansService, getOptions} from './plans.class.js'
import {plansPath, plansMethods} from './plans.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {_get, getJoin, logChange, logHistory, plainTextSanitize, scrub, scrubUploads} from '../../utils/index.js';
import {ObjectId} from 'mongodb';
import {_flatten} from 'symbol-ucan';

export * from './plans.class.js'
export * from './plans.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['plans', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['plans', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };
    const cap_subjects:any = []
    let existing: any = {org: context.data?.org};
    if (!['get', 'find'].includes(context.method)) {
        if (context.method !== 'create') {
            existing = await loadExists(context);
            context = setExists(context, existing);
        }
        const orgId = existing?.org || context.data.org
        const orgNamespace = `orgs:${orgId}`;
        cap_subjects.push(orgId);
        const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin']]
        for (const w of orgWrite) {
            // ucanArgs.create.unshift(w);
            ucanArgs.patch.unshift(w);
            ucanArgs.update.unshift(w);
            ucanArgs.remove.unshift(w);
        }
        const planId = context.id
        if(planId){
            cap_subjects.push(planId);
            const planWrite:CapabilityParts = [`plans:${planId}`, 'planAdmin']
            ucanArgs.patch.unshift(planWrite);
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        cap_subjects,
        or: '*'
    })(context) as any;
}

const handleDocPaths = async (context: HookContext): Promise<HookContext> => {
    const {$set} = context.data || {$set: false};
    if ($set) {
        const patchArr: any = [];
        for (const k in $set) {
            if (/(cafe|hra)\.\w+\.doc/.test(k)) {
                context.data.$set[k] = typeof $set[k] === 'string' ? ObjectId.createFromHexString($set[k]) : $set[k];
                patchArr.push([$set[k], k])
            }
        }
        const patchDocs = async (a: [string, string][]) => {
            await new CoreCall('plan-docs', context, {skipJoins: true}).patch(a[0] as any, {path: a[1]}, {admin_pass: true})
        };
        await Promise.all(patchArr.map(a => patchDocs(a)))
    }
    return context;
}

const runJoins = async (context: HookContext): Promise<HookContext> => {
    const {plan_org} = context.params.runJoin || {};
    if (plan_org) return getJoin({herePath: 'org', service: 'orgs'})(context);
    return context;
}

const checkCoverages = async (context: HookContext): Promise<HookContext> => {
    const {coverages, $set} = context.data || {};
    const ids:any = [];
    let patchCoverages:any = [];
    const patchObj:any = {};
    let runPatch = false;
    for(const k in context.result.coverages || {}){
        ids.push(k)
        const { type, id } = context.result.coverages[k];
        if(!type || !id) patchCoverages.push(k);
    }
    const cvgs = await new CoreCall('coverages', context, {skipJoins: true}).find({
        query: {
            $limit: (patchCoverages.length || 1),
            _id: {$in: patchCoverages}
        }
    })
        .catch(err => {
            console.error(`Error getting coverages at plan coverage check for plan ${context.result._id}: ${err.message}`);
            return {data: []}
        })
    for(const c of cvgs.data){
        if(!patchObj.$set) patchObj.$set = {};
        patchObj.$set[`coverages.${c._id}.type`] = c.type;
        patchObj.$set[`coverages.${c._id}.id`] = c._id;
        runPatch = true;
    }
    if (ids && (coverages || ($set && Object.keys($set).some(a => /\bcoverages\./.test(a)))) && !context.params.checking_coverages) {
        const all = await new CoreCall('coverages', context, {skipJoins: true}).find({
            query: {
                $limit: (ids.length || 1),
                _id: {$in: ids}
            }
        });
        if (all.total !== ids.length) {
            const $unset = {};
            const dataids = all.data.map(a => String(a._id));
            for (const id of ids) {
                if (!dataids.includes(String(id))) $unset[`coverages.${id}`] = '';
            }
            patchObj.$unset = $unset;
            runPatch = true;
        }
        if (runPatch) {
            context.params.checking_coverages = true;
            context.result = await new CoreCall('plans', context).patch(context.id as any, patchObj, {admin_pass: true, checking_coverages: true})
        }
    }
    return context;
}

// import { getCoverageCopy } from './utils/schemas.js';

// const copyCoverages = (context:HookContext) => {
//     const getCoveragePaths = (obj) => {
//         for(const k in obj){
//             if(k.includes('coverages')){
//                 obj[k] = getCoverageCopy(obj[k]);
//             }
//         }
//         return obj;
//     }
//     const { coverages, ...rest } = context.data
//     if(coverages){
//         for(const k in coverages){
//             coverages[k] = getCoverageCopy(coverages[k])
//         }
//     }
//     if(context.data.$set) context.data.$set = getCoveragePaths(context.data.$set);
//     context.data = {...getCoveragePaths(rest), coverages };
//     return context;
// }
import {no_enrollments, noCams, totalContributions} from './utils/pipelines.js';

export const runPipelines = async (context: HookContext) => {
    //no_enrollments must be the plan version
    if (context.params.runJoin?.no_enrollments) {
        const pipeline = no_enrollments(context.params.runJoin.no_enrollments);
        const res = await new CoreCall('plans', context).find({query: {_id: context.id, $limit: 1}, pipeline})
        if (res.total) context.result = res.data[0];
    } else if (context.params.runJoin?.no_cams) {
        const pipeline = noCams()
        const res = await new CoreCall('plans', context).find(
            {
                query: {
                    _id: context.id,
                    $limit: 1
                },
                pipeline
            }
        )
        if (res.total) context.result = res.data[0];

    }
    return context;
}

const makeEnrollments = (path) => {
    return async (context: HookContext): Promise<HookContext> => {
        const v = context.result.enrollments[path];
        let members: any[] = [];
        const d = new Date();
        const date = d;
        const getMember = (mbrId, groupId: string) => {
            if (!groupId) return undefined
            return {
                org: context.result.org,
                person: mbrId,
                group: groupId,
                createdAt: date,
                createdBy: {
                    login: context.params.login?._id,
                    longtail: context.params.core?.ltail,
                    origin: context.params.core?.origin
                },
                open: v.open || date,
                close: v.close,
                plan: context.id,
                name: v.description || `Enrollment ${path.split('_')[0]} - ${path.split('_')[1] || '*'}`,
                version: path,
                planYear: path.split('_')[0],
                status: 'not_started'
            }
        }
        if (v.open_enroll) {
            const grps = await new CoreCall('groups', context, {skipJoins: true}).find({
                query: {_id: {$in: context.result.groups}},
                admin_pass: true
            })
            members = _flatten((grps.data || []).map(a => (a.members || []).map(b => getMember(b, a._id)))).filter(a => !!a);
        } else {
            if (v.groups) {
                const grps = await new CoreCall('groups', context, {skipJoins: true}).find({
                    query: {_id: {$in: v.groups}},
                    admin_pass: true
                })
                let ppls: { data: Array<any>, total: number, limit: number, skip: number } = {
                    data: [],
                    total: 0,
                    limit: 0,
                    skip: 0
                };
                if (v.ppls) {
                    ppls = await new CoreCall('ppls', context, {skipJoins: true}).find({
                        query: {
                            $limit: v.ppls.length,
                            _id: {$in: v.ppls}
                        },
                        admin_pass: true,
                        runJoin: {orgGroup: context.result.org}
                    })
                }
                members = [..._flatten((grps.data || []).map(a => a.members.map(b => getMember(b, a._id)))).filter(a => !!a), ...(ppls.data || []).map(a => getMember(a._id, _get(a, '_fastjoin.orgGroups[0]._id') as any))].filter(a => !!a);
            }
        }
        if (members?.length) {
            const loopOne = async (start = 0): Promise<number> => {
                const mbrs = members.slice(start, start + 500);
                const next = await new CoreCall('enrollments', context).create(mbrs, {
                    admin_pass: true,
                    _from_plan: 500
                })
                if (next && (start + 500 < members.length)) return await loopOne(start + 500)
                else if (!next) return start;
                else return members.length;
            }
            const sentThrough = v.sentThrough || 0;
            const sent = await loopOne(sentThrough);
            context.result = await new CoreCall('plans', context).patch(context.id as any, {$set: {[`enrollments.${path}.sentThrough`]: sent}})
        }
        return context;
    }
}

const activateEnrollment = async (context: HookContext): Promise<HookContext> => {
    if (context.type === 'before') {
        const a = context.data._activate;
        if (a) {
            context.params._activate_enrollment = a;
            context.data.$set = {[`enrollments.${a}.active`]: true}
            delete context.data._activate;
            return context;
        }
        return context;
    } else {
        const a = context.params._activate_enrollment;
        if (a) {
            const v = context.result.enrollments[a];
            if (v) return await makeEnrollments(a)(context);
        }
        return context;
    }
}

const handlePlanNumber = async (context: HookContext): Promise<HookContext> => {
    const {org, template, info} = context.data;
    if (!org && !template) throw new Error('Non template plans must have a plan sponsor')
    else if (org) {
        const query: any = {$limit: 100, org, $sort: {number: -1}};
        if (info?.number) query['info.number'] = info.number;
        const existing = await new CoreCall('plans', context, {skipJoins: true}).find({query, disableSoftDelete: true});
        if (existing.total) {
            if (info?.number) throw new Error(`This sponsor already has a plan with the number ${info.number} - pick a new number`);
            else context.data.info = {...context.data.info, number: String(Number(existing.data[0] || 500) + 1)}
        } else if (!info?.number) context.data.info = {...context.data.info || {}, number: '501'}
    }
    return context;
}

const estimateFte = async (context: HookContext): Promise<HookContext> => {
    if (!context.result.ale) {
        const {groups, $addToSet, $pull, $set} = context.data;
        let run = false;
        if (groups) run = true;
        else if ($addToSet?.groups) run = true;
        else if ($pull?.groups) run = true;
        else if ($set?.groups) run = true;
        if (run) {
            let fte = 0;
            let ale = false;
            const grps = await new CoreCall('groups', context, {skipJoins: true}).find({query: {_id: {$in: context.result.groups}}})
                .catch(err => {
                    console.error(`Error getting groups at plan fte check for plan ${context.result._id}: ${err.message}`);
                    return {data: []}
                })
            let mbrs: any = []
            for (const g of grps.data) {
                mbrs = [...mbrs, ...g.members || []]
            }
            if (mbrs.length >= 50) {
                if (mbrs.length < 150) {
                    if (!context.result.estFte) {
                        const cams = await new CoreCall('cams', context, {skipJoins: true}).find({
                            query: {
                                $limit: 250,
                                person: {$in: mbrs, org: {$in: grps.data.map(a => a.org)}}
                            }
                        })
                            .catch(err => {
                                console.error(`Error getting cams at plan fte check for plan ${context.result._id}: ${err.message}`);
                                return {data: []}
                            })
                        for (const cam of cams.data) {
                            const hrs = cam.hoursWorked || cam.estHours;
                            if (hrs >= 30) fte += 1;
                            else fte += (hrs / 30);
                        }
                        if (fte >= 50) {
                            ale = true;
                            fte = Math.ceil(fte);
                        }
                    }
                } else {
                    ale = true;
                    if (!context.result.estFte) fte = mbrs.length * .8
                }
            }
            if (ale) {
                const patchObj: any = {ale}
                if (fte) patchObj.estFte = fte;
                context.result = await new CoreCall('plans', context).patch(context.result._id, patchObj, {admin_pass: true})
            }
        }
    }
    return context;
}

const updateContributions = async (context: HookContext): Promise<HookContext> => {
    const {$set} = context.data;
    if ($set) {
        let version;
        for (const k in $set) {
            if (/^enrollments\.[^.]+\.lastUpdate$/.test(k)) {
                version = k.split('.')[1]
                break;
            }
        }
        const {update_coverage_contributions} = context.params.runJoin || {}

        if (version || update_coverage_contributions) {
            const existing = await loadExists(context);
            context = setExists(context, existing);
            if (update_coverage_contributions) {
                const version = getOpenEnrolls(existing)[0];
                const enrolled = await new CoreCall('enrollments', context).find({
                    paginate: false,
                    query: {
                        plan: context.id,
                        version,
                        status: 'complete'
                        // TODO: removed to consolidate counting enrollments - check that not having any coverages does not cause additional burden
                        // [`coverages.${update_coverage_contributions}.enrolled[0]`]: {$exists: true}
                    }
                })
                if (enrolled.length) {
                    context.data.$set = {...context.data.$set, [`enrollments.${version}.enrolled`]: enrolled.length}
                    const runUpdate = async (ermt: any) => {
                        await new CoreCall('enrollments', context).patch(ermt._id, {updatedAt: new Date()}, {runJoin: {plan_enrollment_update: true}});
                    }
                    await Promise.all(enrolled.map(a => runUpdate(a)));
                }
            }

            const versions = [version];
            for (const v in existing.enrollments) {
                if (!existing.enrollments[v].open_enroll) versions.push(v);
            }
            const pipeline = totalContributions(existing)

            const data = await new CoreCall('enrollments', context)._find({
                skip_hooks: true, admin_pass: true,
                query: {
                    deleted: { $ne: true },
                    version: {$in: versions},
                    plan: typeof context.id === 'string' ? ObjectId.createFromHexString(context.id as string) : context.id,
                    status: 'complete',
                    terminatedAt: {$exists: false}
                },
                pipeline,
                paginate: false
            });
            if (data[0]) context.data.$set = {
                ...context.data.$set,
                [`enrollments.${version}.contributions`]: data[0].contributions
            }
        }
    }
    return context;
}

import {getLatestOpenObj, getOpenEnrolls} from './utils/index.js';

const protectPostEnrollment = async (context: HookContext): Promise<HookContext> => {
    if (!context.params.special_change) {
        const ex = await loadExists(context);
        context = setExists(context, ex);
        const latestOpen: any = getLatestOpenObj(ex);
        if (latestOpen?.close) {
            const close = new Date(latestOpen.close).getTime();
            if (close < new Date().getTime()) {
                const pl = ['cafe', 'hra', 'coverages', 'employerContribution']
                const throwIt = (k) => {
                    throw new Error(`You cannot make changes to ${k} on this plan because it is post enrollment. If you need to make major plan changes - create a copy of your plan and re-enroll your participants.`)
                }
                for (let i = 0; i < pl.length; i++) {
                    if (context.data[pl[i]]) throwIt(pl[i])
                    else {
                        for (const k in context.data.$set || {}) {
                            if (k.startsWith(pl[i])) throwIt(pl[i])
                        }
                    }
                }
            }
        }
    }
    return context;
}

const sanitizeConflicts = (context: HookContext): HookContext => {
    const teamSet: string[] = [];
    for (const k in context.data.$set || {}) {
        if (/team./.test(k)) teamSet.push(k);
    }
    if (context.data.team) {
        if (context.data.team) {
            for (const k in context.data.team) {
                context.data.team[k] = {
                    ...context.data.team[k],
                    conflicts: plainTextSanitize(context.data.team[k].conflicts || ''),
                    feeDescription: plainTextSanitize(context.data.team[k].feeDescription || ''),
                    roleDescription: plainTextSanitize(context.data.team[k].roleDescription || ''),
                }
            }
        }
    }
    if (teamSet.length) {
        for (const k of teamSet) {
            if (k.split('.').length === 2) {
                context.data.$set[k] = {
                    ...context.data.$set[k],
                    conflicts: plainTextSanitize(context.data.$set[k].conflicts || ''),
                    feeDescription: plainTextSanitize(context.data.$set[k].feeDescription || ''),
                    roleDescription: plainTextSanitize(context.data.$set[k].roleDescription || '')
                }
            } else if (/.conflicts/.test(k) || /.feeDescription/.test(k) || /.roleDescription/.test(k)) {
                context.data.$set[k] = plainTextSanitize(context.data.$set[k])
            }
        }
    }
    return context;
}

const convertTeamIds = (context:HookContext):HookContext => {
    if(context.params._search?.teamId){
        context.params.query[`team.${context.params._search.teamId}.id`] = ObjectId.createFromHexString(context.params._search.teamId);
    }
    return context;
}
import {autoCreate} from '../caps/utils/index.js';

const paths = ['files.*'];
const uploadsConfig = {paths};
// A configure function that registers the service and its hooks via `app.configure`
export const plans = (app: Application) => {
    // Register our service on the Feathers application
    app.use(plansPath, new PlansService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: plansMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(plansPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(plansExternalResolver),
                schemaHooks.resolveResult(plansResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(plansQueryValidator),
                schemaHooks.resolveQuery(plansQueryResolver),
                scrubUploads(uploadsConfig)
            ],
            find: [convertTeamIds],
            get: [runPipelines],
            create: [
                handlePlanNumber,
                schemaHooks.validateData(plansDataValidator),
                schemaHooks.resolveData(plansDataResolver),
                scrub(['terms.*.body']),
                handleDocPaths
            ],
            patch: [
                activateEnrollment,
                schemaHooks.validateData(plansPatchValidator),
                schemaHooks.resolveData(plansPatchResolver),
                protectPostEnrollment,
                logHistory(['active', 'files', 'procedureWhitelist', 'procedureBlacklist', 'info', 'employerContribution', 'cafe', 'coverages', 'canContribute', 'limit']),
                scrub(['terms.*.body']),
                handleDocPaths,
                updateContributions,
                sanitizeConflicts
            ],
            remove: []
        },
        after: {
            all: [runJoins, scrubUploads(uploadsConfig)],
            create: [
                autoCreate('plans', 'admins'),
                estimateFte
            ],
            patch: [
                estimateFte,
                activateEnrollment,
                checkCoverages
            ],
            find: [],
            get: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [plansPath]: PlansService
    }
}
