import {parsePhoneNumber} from 'awesome-phonenumber';

import {
    buildUcan,
    encodeKeyPair,
    ucanToken,
    genCapability,
    Capability,
    _get,
    _set
} from 'symbol-ucan';
import {iff, preventChanges} from 'feathers-hooks-common';
import {hooks as localHooks} from '@feathersjs/authentication-local';

const {protect} = localHooks;

import {
    relate,
    checkExisting,
    genEd255519KeyPair,
    publicKeyToDid,
    symmetricEncrypt, getJoin, logChange
} from '../../../utils/index.js';

import {allUcanAuth, CapabilityParts, bareAuth, CoreCall, AnyObj, checkUcan} from 'feathers-ucan';

const creator = [['logins', 'WRITE']] as Array<CapabilityParts>;
const deleter = [['logins', '*']] as Array<CapabilityParts>;
const ucanArgs = {
    patch: [...creator],
    // get: [['logins', 'READ']] as Array<CapabilityParts>,
    update: [...creator],
    remove: deleter
}

import {sendVerifyEmail} from '../../auth-management/email/index.js';
import {sendVerifyText} from '../../auth-management/sms/index.js';
import {fromString, toString} from "uint8arrays";
import {hooks as schemaHooks} from '@feathersjs/schema'
import {
    loginsExternalResolver,
    loginsResolver,
    loginsQueryValidator,
    loginsDataValidator,
    loginsPatchValidator,
    loginsQueryResolver, loginsDataResolver, loginsPatchResolver
} from '../logins.schema.js';
import {HookContext} from '../../../declarations.js';
// import {limitTo} from '../../groups/utils/search-members.js';

const encryptionKey = process.env.PIN_ENCRYPTOR || '83bca3cf17b4a6525e3895863fce6805a4aae2002915d763fc000dd56044d530';

const getLoginOptions = (context: AnyObj) => {
    return context.params.query?.loginOptions || context.params.loginOptions || {};
}

export const verifyOAuth = context => {
    if (context.data.googleId || context.data.facebookId || context.data.linkedinId || context.data.githubId || context.data.microsoftId) {
        context.data.isVerified = true;
        context.data.verifyToken = null;
        context.data.verifyExpires = null;
    }
};

const createUcan = async (context) => {
    if (!context.result?.ucan) {

        const {defaultScheme, defaultHierPart} = context.app.get('authentication')

        let {did} = context.data;
        if (!did) {
            const keyPair = genEd255519KeyPair();
            did = publicKeyToDid(undefined, fromString(keyPair.publicKey), 'ed25519');
            context.data.did = did;
            const {encrypt} = symmetricEncrypt(encryptionKey)
            context.data.keyPair = {
                publicKey: keyPair.publicKey,
                privateKey: toString(encrypt(keyPair.privateKey), 'hex'),
                alg: 'ed255519'
            };
        }
        context.params.ucan_audience = did;
        const {secret} = context.app.get('authentication');
        const canEditMe = {
            with: {scheme: defaultScheme, hierPart: defaultHierPart},
            can: {namespace: `logins:${did}`, segments: ['*']}
        }
        let opts = {
            audience: did,
            issuer: encodeKeyPair({secretKey: secret}),
            lifetimeInSeconds: 60 * 60 * 24 * 30,
            proofs: [],
            capabilities: [...((context.params.ucan_caps || []) as Array<Partial<Capability>>).map(a => genCapability(a, {
                defaultScheme,
                defaultHierPart
            })), canEditMe]
        };

        const ucan = await buildUcan(opts);
        if (!did || !ucan) throw new Error(`Failed creating ucan for new login. DID ${did} - UCAN ${ucan}`)
        const token = ucanToken(ucan);
        context.data.ucan = token;
        context.params.ucan = token;
    }
    return context;
};

export const setVerify = (loginData: AnyObj) => {
    return async (context: any): Promise<HookContext> => {
        if (!loginData.isVerified) {
            let {login, token} = await context.app.service('auth-management').create({
                login: {...loginData},
                action: 'setVerify',
                options: getLoginOptions(context)
            });
            context.params.verifyToken = token;
            if (context.params._loginExists) {
                // console.log('verify expires', login.verifyExpires, typeof login.verifyExpires);
                login = await new CoreCall('logins', context).patch(context.result._id, {
                    verifyToken: login.verifyToken,
                    verifyExpires: login.verifyExpires
                }, {admin_pass: true, skip_hooks: true})
            }
            context.data = login;
            return context;
        } else return context;
    }
};

export const setReset = (loginData?: AnyObj) => {
    return async (context: any): Promise<HookContext> => {
        let {login, token} = await context.app.service('auth-management').create({
            login: {_id: context.id, ...context.data, ...loginData},
            action: 'setResetPassword',
            options: getLoginOptions(context)
        }, {core: context.params.core});
        context.params.resetToken = token;

        // console.log('reset token expires', login.verifyExpires, typeof login.verifyExpires);
        login = await new CoreCall('logins', context).patch(loginData?._id || context.id, {
            resetToken: login.resetToken,
            resetExpires: login.resetExpires
        }, {admin_pass: true})

        return {...context, result: login};
    };
}

export const sendPin = async (context: HookContext): Promise<HookContext> => {
    if (!context.result.isVerified) {
        const {key} = context.app.get('mailer') || {key: null};
        const {key: sms_key, id, from} = context.app.get('sms') || {key: null};
        const {verifyToken} = context.params;
        const loginOptions = getLoginOptions(context);
        const methods = {
            'email': (pin, lg) => sendVerifyEmail(pin, lg, {key}),
            'sms': (pin, lg) => sendVerifyText(pin, lg.phone.number.e164, {key: sms_key, id, from})
        };
        await methods[loginOptions?.method || 'email'](verifyToken, context.result);
    }
    return context;
};

export const resendPin = async (context: HookContext): Promise<HookContext> => {
    const loginOptions = getLoginOptions(context);
    if (loginOptions?.resend) {
        context.params.query.loginOptions.method = loginOptions.resend;
        context.params._loginExists = true;
        if (!context.result) {
            context.result = await new CoreCall('logins', context).get(context.id as any, {admin_pass: true});
        }
        context = await setVerify(context.result)(context);
        context = await sendPin(context);
    }
    return context;
}

export const verifyPin = async context => {
    const loginOptions = getLoginOptions(context);
    if (loginOptions?.verify) {
        const id = context.id;
        const existingLogin = await new CoreCall('logins', context).get(id);
        // console.log('verify token', existingLogin, context.id);
        const {status, message} = await context.app.service('auth-management').create({
            login: existingLogin,
            action: 'verify',
            options: {token: loginOptions.pin}
        }, {core: context.params.core});
        if (status !== 0) throw new Error(message);
        else context.result = await new CoreCall('logins', context).patch(context.id, {
            isVerified: true,
            verifyToken: null,
            verifyExpires: null
        }, {admin_pass: true});
    } else if (loginOptions?.resetPassword) {
        const existingLogin = await new CoreCall('logins', context).get(context.id);
        const {login, status, message} = await context.app.service('auth-management').create({
            login: {...existingLogin, pendingPassword: context.data.pendingPassword},
            action: 'resetPassword',
            options: {token: loginOptions.pin}
        }, {core: context.params.core});
        if (status !== 0) throw new Error(message);
        context.result = await new CoreCall('logins', context).patch(context.id, login, {admin_pass: true})
    }
    return context;
};

export const relateOwner = {
    paramsName: 'relateOwner',
    herePath: 'owner',
    therePath: 'login',
    thereService: 'ppls'
};

import {combineUcan} from '../../caps/caps.js';

const runInvites = async (context: HookContext): Promise<HookContext> => {
    if(!context.result.owner) return context;
    const person = context.result._fastjoin?.owner || await new CoreCall('ppls', context).get(context.result.owner as any)
    /** Take invites - validate the "by" inviter has the capability to invite someone to this cap and add the login id to the cap */
    if (person.invites) {
        let removeSome = false;
        const removeUnset: any = {}
        const byCapId: any = {};
        const capIds: any = [];
        for (const k in person.invites) {
            const {by, caps} = person.invites[k] || {}
            if (by && caps) {
                for (const capId in caps) {
                    const {id, path} = caps[capId] || {}
                    if (id && path) {
                        capIds.push(id)
                        byCapId[id] = {...byCapId[id], [path]: [...byCapId[id][path] || [], by]};
                    } else {
                        removeSome = true;
                        removeUnset[`invites.${k}.caps.${capId}`] = ''
                    }
                }
            } else {
                removeSome = true;
                removeUnset[`invites.${k}`] = ''
            }
        }
        /** remove invalid invites */
        if (removeSome) await new CoreCall('ppls', context).patch(person._id, {$unset: removeUnset}, {admin_pass: true})
            .catch(err => {
                console.error(`Error removing invites from person on first login ${person._id}: ${err.message}`)
            })

        const caps = await new CoreCall('caps', context, {skipJoins: true}).find({
            query: {
                $limit: capIds.length,
                _id: {$in: capIds}
            }
        })
            .catch(err => {
                console.error(`Error looking up caps for invites on first login ${person._id}: ${err.message}`)
            })

        /** Filter through and ensure the inviter still has the ability to grant this capability */
        let patchCaps = false;
        const capPatchById:any = {}
        for (const cap of caps.data) {
            const capObj:any = byCapId[cap._id];
            if(!capObj) continue;
            const capCaps = cap.caps || {}
            for(const path in capObj){
                const logins = capCaps[path]?.logins?.map(a => String(a))
                if(capObj[path].some(a => logins.includes(a))){
                    patchCaps = true;
                    capPatchById[cap._id] = {...capPatchById[cap._id], [path]: true }
                }
            }
        }
        /** Add the login to the capability record */
        if(patchCaps){
            const capPromises:any = [];
            for(const id in capPatchById){
                const obj:any = {};
                for(const path in capPatchById[id]){
                    obj[`caps.${path}.logins`] = context.result._id;
                }
                capPromises.push(new CoreCall('caps', context).patch(id, { $addToSet: obj }, {admin_pass: true})
                    .catch(err => {
                        console.error(`Error patching cap with logins on first login capId:${id}; person: ${person._id}; err: ${err.message}`)
                    }))
            }
            await Promise.all(capPromises)
        }
    }
    return context;
}

const addOwner = async (context: HookContext): Promise<HookContext> => {
    const {email, phone} = context.data;
    if ((email || phone) && !context.data.owner) {
        let person: any;
        const query = {};
        if (email && phone) query['$or'] = [
            {email},
            {'phone.number.e164': phone}
        ];
        else if (email) query['email'] = email;
        else if (phone) query['phone.number.e164'] = phone;
        else throw new Error('Must have an email or phone number to create a new login');
        const existing = await new CoreCall('ppls', context, {skipJoins: true}).find({query, admin_pass: true});
        if (existing.total) {
            person = existing.data[0];
            let memberOf: any = {};
            if (person?._id) memberOf = await new CoreCall('groups', context, {skipJoins: true}).find({query: {members: {$in: [person._id]}}})
            if (memberOf.total) {
                /** Apply any group ucans and also check that the person has inGroups set - just since we already made the query here, it's a good effort to sync data */
                const applyUcans: any = [];
                let patchPerson = false;
                const prsnPatch: any = {$addToSet: {inGroups: {$each: []}}}
                for (const grp of memberOf.data) {
                    if (grp.applyUcan) applyUcans.push(grp.applyUcan);
                    if (((!person.inGroups || []) as Array<any>).includes(grp._id)) {
                        patchPerson = true;
                        prsnPatch.$addToSet.inGroups.$each.push(grp._id)
                    }
                }
                if (patchPerson) {
                    await new CoreCall('ppls', context).patch(person._id, prsnPatch, {admin_pass: true})
                        .catch(err => console.error(`Error adding groups to person ${person._id}: ${err.message}`))
                }
                for (const i of applyUcans) {
                    context.data.ucan = await combineUcan(context.data.ucan, applyUcans[i])(context)
                }

                context.params.ucan = context.data.ucan;
            }
        } else {
            const owner = {
                email,
                phone: phone ? parsePhoneNumber(phone) : undefined
            };
            person = await new CoreCall('ppls', context).create(owner, {admin_pass: true});
        }
        context.data.owner = person._id;
    }
    return context;
};

// const clearPpls = async (context) => {
//     const existing = await new CoreCall('logins', 'get', context, { skipJoins: true })(context.id);
//
//
// }
const checkFp = async (context: HookContext): Promise<HookContext> => {
    const fpId = context.params.core?.fp;
    if (fpId) {
        await new CoreCall('fingerprints', context).patch(fpId, {login: context.result._id}, {admin_pass: true})
        const refs = await new CoreCall('refs', context, {skipJoins: true}).find({
            query: {fingerprint: fpId},
            admin_pass: true
        })
        if (refs.total) {
            await new CoreCall('refs', context).patch(refs.data[0]._id, {referred: context.result._id}, {admin_pass: true})
        }
    }
    return context;
}

const runJoins = async (context: HookContext): Promise<HookContext> => {
    const {runJoin} = context.params;
    if (runJoin) {
        if (runJoin.login_person) return getJoin({
            service: 'ppls',
            herePath: 'owner',
            params: {skip_hooks: true}
        })(context)
    }
    return context;
}

import {webAuthnOptions} from './webauthn/index.js';

const postCreateAuth = async (context: HookContext): Promise<HookContext> => {
    const loginOptions = getLoginOptions(context);
    if (loginOptions.method === 'webauthn') return await webAuthnOptions('register')(context);
    return sendPin(context);
}

const postPatchAuth = async (context: HookContext): Promise<HookContext> => {
    const loginOptions = getLoginOptions(context);
    if (loginOptions.method === 'webauthn') {
        const action = loginOptions.authAction ? loginOptions.authAction : context.result.isVerified ? 'authenticate' : 'register';
        return await webAuthnOptions(action)(context);
    }
    return context;
}


export const loginHooks = {
    around: {
        all: [
            schemaHooks.resolveExternal(loginsExternalResolver), schemaHooks.resolveResult(loginsResolver)
        ]
    },
    before: {
        all: [
            logChange(),
            schemaHooks.validateQuery(loginsQueryValidator),
            schemaHooks.resolveQuery(loginsQueryResolver)
        ],
        find: [
            async context => {
                const loginOptions = getLoginOptions(context);
                const reset = loginOptions?.setResetPassword;
                if (context.params.provider && !loginOptions?.existCheck && !reset) bareAuth<HookContext>(context)
                else if (reset) {
                    const {loginOptions, ...rest} = context.params.query;
                    const find = await context.app.service('logins').find({query: {...rest, $limit: 1, $skip: 0}});
                    if (find.total) {
                        context = await setReset(find.data[0])(context);
                        context.result = {limit: 1, skip: 0, total: 1, data: [context.result]}
                    }
                }
                return context;
            }
        ],
        get: [
            // ctx => {
            //     console.log(ctx);
            // }
        ],
        create: [
            checkExisting(['email', 'phone'], '_loginExists'),
            async ctx => {
                if (ctx.result) {
                    const loginOptions = getLoginOptions(ctx);
                    if (loginOptions?.resend) return await resendPin(ctx);
                    return ctx;
                } else {
                    const loginOptions = getLoginOptions(ctx);
                    ctx.params.loginOptions = loginOptions;
                    if (ctx.params.query) delete ctx.params.query.loginOptions;
                    ctx = await createUcan(ctx);
                    ctx = await addOwner(ctx);
                    if (loginOptions.method !== 'webauthn') ctx = await setVerify(ctx.data)(ctx);
                    await schemaHooks.validateData(loginsDataValidator)(ctx);
                    await schemaHooks.resolveData(loginsDataResolver)(ctx);
                    let password = _get(ctx, 'data.password');
                    if (password) {
                        ctx.params = _set(ctx.params, 'password', password);
                    }
                    verifyOAuth(ctx);
                }
                return ctx;
            }
        ],
        update: [
            allUcanAuth<HookContext>(ucanArgs, {loginPass: [[['_id'], ['update']]], adminPass: ['update']}),
            schemaHooks.validateData(loginsPatchValidator),
            schemaHooks.resolveData(loginsPatchResolver),
            (context: HookContext) => {
                iff(context.params.provider,
                    preventChanges(
                        true,
                        'email',
                        'phone',
                        'password',
                        'isVerified',
                        'verifyToken',
                        'verifyExpires',
                        'resetToken',
                        'resetExpires',
                        'keyPair'
                    ),
                )(context)
                return context;
            },

            relate('oto', relateOwner)
        ],
        patch: [
            async (context: HookContext) => {
                if (context.data?.loginAttempts) {
                    context.data.$push = {
                        ...context.data.$push,
                        loginAttempts: {value: new Date(), $position: 0, $slice: 20}
                    }
                    delete context.data.loginAttempts;
                }
                const loginOptions = getLoginOptions(context);
                context.params.loginOptions = loginOptions;
                if (context.params.query) delete context.params.query.loginOptions;
                if (loginOptions.method === 'webauthn') {
                    context.data = {$push: {loginAttempts: {value: new Date(), $position: 0, $slice: 20}}}
                    context.params.admin_pass = true;
                } else if (loginOptions.resend) {
                    context = await resendPin(context)
                } else if (loginOptions.verify) {
                    context = await verifyPin(context);
                    iff(context.params.provider === 'external',
                        preventChanges(
                            true,
                            'verifyToken',
                            'isVerified',
                            'email',
                            'phone',
                            'password',
                            'keyPair'
                        )
                    )(context)
                } else if (!context.params._loginExists && !loginOptions?.setResetPassword && !loginOptions?.resetPassword) {
                    if (context.params.login?._id !== context.id && !context.params.skip_hooks) context = await allUcanAuth<HookContext>(ucanArgs, {
                        adminPass: ['patch'],
                        loginPass: [[['_id'], ['*']]]
                    })(context);
                    // if(!context.id === context.params.user?._id);
                } else if (loginOptions?.setResetPassword) {
                    context = await setReset()(context);
                }
                let password = _get(context, 'data.password');
                if (password) {
                    context.params = _set(context.params, 'password', password);
                }
                return context;
            },
            schemaHooks.validateData(loginsPatchValidator),
            schemaHooks.resolveData(loginsPatchResolver),
            // hashPassword('password'),
            relate('oto', relateOwner)
        ],
        remove: [
            allUcanAuth<HookContext>(ucanArgs, {loginPass: [[['_id'], ['remove']]]}),
            // clearPpls,
            relate('oto', relateOwner)
        ]
    },
    after: {
        all: [
            // Make sure the password field is never sent to the client
            // Always must be the last hook
            protect('password', 'keyPair'),
            runJoins
        ],
        find: [],
        get: [],
        create: [
            relate('oto', relateOwner),
            postCreateAuth,
            checkFp,
            runInvites
        ],
        update: [relate('oto', relateOwner)],
        patch: [
            relate('oto', relateOwner),
            postPatchAuth
        ],
        remove: [relate('oto', relateOwner)]
    },

    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: []
    }
}
