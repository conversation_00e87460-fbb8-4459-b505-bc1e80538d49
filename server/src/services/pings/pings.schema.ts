
// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, getValidator, querySyntax } from '@feathersjs/schema'
import { ObjectIdSchema } from '@feathersjs/schema'
import type { FromSchema } from '@feathersjs/schema'

import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonFields, commonPatch, commonQueries, defResolver} from '../../utils/common/schemas.js';

const sendSchema = {
  type: 'object',
  properties: {
    send: { type: 'boolean' },
    sent: { type: 'string' },
    unsubbed: { type: 'boolean' },
    error: { type: 'string' },
    errorMessage: { type: 'string' },
    tries: { type: 'number' },
    opened: { type: 'string' }
  }
} as const
// Main data model schema
export const pingsSchema = {
  $id: 'Pings',
  type: 'object',
  additionalProperties: false,
  required: ['_id'],
  properties: {
    _id: ObjectIdSchema(),
    subject: ObjectIdSchema(),
    subjectService: { type: 'string' },
    recipient: ObjectIdSchema(),
    recipientService: { type: 'string' }, //should be ppls
    nouns: { type: 'array', items: { type: 'string' }}, //who did something
    verbs: { type: 'array', items: { type: 'string' }}, //what they did
    message: { type: 'string' },
    subjectAvatarPath: { type: 'string' },
    subjectNamePath: { type: 'string' },
    link: { type: 'string' },
    tries: { type: 'number' }, //use for patching
    action: { type: 'string' }, //a way to commonly categorize what has happened - less general than categories
    priority: { type: 'number' }, //lower is higher under 5 will go internally no matter what and under 3 will go to email no matter what
    category: { type: 'string' }, //meant to be a sort of general category for notification management
    methods: {
      type: 'object',
      properties: {
        internal: sendSchema,
        email: sendSchema,
        sms: sendSchema
      }
    },
    ...commonFields.properties
  }
} as const
export type Pings = FromSchema<typeof pingsSchema>
export const pingsValidator = getValidator(pingsSchema, dataValidator)
export const pingsResolver = resolve<Pings, HookContext>({})

export const pingsExternalResolver = resolve<Pings, HookContext>({})

// Schema for creating new data
export const pingsDataSchema = {
  $id: 'PingsData',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...pingsSchema.properties
  }
} as const
export type PingsData = FromSchema<typeof pingsDataSchema>
export const pingsDataValidator = getValidator(pingsDataSchema, dataValidator)
export const pingsDataResolver = resolve<PingsData, HookContext>({
  properties: {
    recipientService: defResolver('logins'),
    category: defResolver('general'),
    priority: async (val) => {
      if((val || val === 0) && val > -1) return val;
      return 10;
    },
    methods: async (val) => {
      return {
        internal: {send: true},
        email: {send: false},
        sms: {send: false},
        ...val
      }
    }
  }
})

// Schema for updating existing data
export const pingsPatchSchema = {
  $id: 'PingsPatch',
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    ...pingsSchema.properties,
    ...commonPatch(pingsSchema.properties).properties,
    'methods.internal.opened': { type: 'boolean' }
  }
} as const
export type PingsPatch = FromSchema<typeof pingsPatchSchema>
export const pingsPatchValidator = getValidator(pingsPatchSchema, dataValidator)
export const pingsPatchResolver = resolve<PingsPatch, HookContext>({})

// Schema for allowed query properties
export const pingsQuerySchema = {
  $id: 'PingsQuery',
  type: 'object',
  additionalProperties: true,
  properties: {
    ...querySyntax({...pingsSchema.properties, ...commonQueries.properties})
  }
} as const
export type PingsQuery = FromSchema<typeof pingsQuerySchema>
export const pingsQueryValidator = getValidator(pingsQuerySchema, queryValidator)
export const pingsQueryResolver = resolve<PingsQuery, HookContext>({})
