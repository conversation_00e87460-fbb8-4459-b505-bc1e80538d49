import { HookContext} from '../../declarations.js';

/** pass patch validators for special paths without needing to compromise validator integrity
 * @param {object} paths: an object with keys for the top level mongo operator data key and values for the subkeys to bypass
 * */

export const bypassValidators = (paths:{[k:string]: Array<RegExp>}) => {
    return (context: HookContext) => {
        for (const k in context.data || {}) {
            if (paths[k]) {
                for (const sub of context.data[k]) {
                    if(paths[k].some((a:any) => a.test(sub))) {
                        if(!context.params.bypassed) context.params.bypassed = {};
                        if(!context.params.bypassed[k]) context.params.bypassed[k] = {};
                        context.params.bypassed[k][sub] = context.data[k][sub];
                        delete context.data[k][sub];
                    }
                }
            }
        }
        return context;
    }
}

export const injectBypassed = (context: HookContext) => {
    if(context.params.bypassed){
        for(const k in context.params.bypassed){
            for(const sub in context.params.bypassed[k] || {}){
                context.data[k][sub] = context.params.bypassed[k][sub];
            }
        }
    }
    return context;
}
