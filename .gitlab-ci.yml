deploy:
  stage: deploy
  environment: stage
  script:
    - echo "Starting Deplyment Process"
    - export AUTH_SECRET=$AUTH_SECRET
    - export MONGO_DB_URI=$MONGO_DB_URI
    - export OAUTH_GOOGLE_KEY=$OAUTH_GOOGLE_KEY
    - export OAUTH_GOOGLE_SECRET=$OAUTH_GOOGLE_SECRET
    - export UCAN_SK=$UCAN_SK
    - export OAUTH_LINKEDIN_KEY=$OAUTH_LINKEDIN_KEY
    - export OAUTH_LINKEDIN_SECRET=$OAUTH_LINKEDIN_SECRET
    - export SENDGRID_API_KEY=$SENDGRID_API_KEY
    - export STORJ_ACCESS_KEY=$STORJ_ACCESS_KEY
    - export STORJ_REGION=$STORJ_REGION
    - export STORJ_BUCKET=$STORJ_BUCKET
    - export STORJ_SECRET_KEY=$STORJ_SECRET_KEY
    - export STORJ_ENDPOINT=$STORJ_ENDPOINT
    - export VUE_APP_FEATHERS_URL=$VUE_APP_FEATHERS_URL
    - export NODE_ENV=$NODE_ENV
    - ./deploy.sh
